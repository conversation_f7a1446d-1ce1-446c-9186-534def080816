"""
Configurações do sistema de reconhecimento facial
"""
import os
from dataclasses import dataclass, field
from typing import Dict, Any

@dataclass
class ModelConfig:
    """Configurações do modelo de reconhecimento"""
    name: str = "Facenet"  # Facenet é mais rápido que VGG-Face
    detector_backend: str = "mtcnn"  # MTCNN é mais preciso que OpenCV
    threshold: float = 0.6
    enforce_detection: bool = False

@dataclass
class CameraConfig:
    """Configurações da câmera"""
    width: int = 640
    height: int = 480
    fps: int = 30
    frame_skip: int = 2  # Processar a cada 2 frames para melhor performance

@dataclass
class ProcessingConfig:
    """Configurações de processamento"""
    max_queue_size: int = 3
    batch_size: int = 1
    enable_gpu: bool = True
    memory_limit_mb: int = 1024
    confidence_threshold: float = 0.6
    # Largura alvo para detecção (reduz custo). Altura mantém proporção
    detection_width: int = 480
    # Intervalo mínimo entre análises completas por câmera (segundos)
    min_analysis_interval: float = 0.2

@dataclass
class UIConfig:
    """Configurações da interface"""
    window_title: str = "Sistema de Reconhecimento Facial"
    show_fps: bool = True
    show_confidence: bool = True
    font_scale: float = 0.6
    font_thickness: int = 2

@dataclass
class SystemConfig:
    """Configuração principal do sistema"""
    known_faces_dir: str = "known_faces"
    embeddings_file: str = "face_embeddings.pkl"
    log_file: str = "face_recognition.log"
    backup_dir: str = "backups"
    
    # Sub-configurações (usar default_factory para evitar instâncias compartilhadas)
    model: ModelConfig = field(default_factory=ModelConfig)
    camera: CameraConfig = field(default_factory=CameraConfig)
    processing: ProcessingConfig = field(default_factory=ProcessingConfig)
    ui: UIConfig = field(default_factory=UIConfig)
    
    def __post_init__(self):
        """Criar diretórios necessários"""
        os.makedirs(self.known_faces_dir, exist_ok=True)
        os.makedirs(self.backup_dir, exist_ok=True)
        # Criar diretório do arquivo de log, se houver subdiretório definido
        log_dir = os.path.dirname(self.log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
    
    def to_dict(self) -> Dict[str, Any]:
        """Converter configuração para dicionário"""
        return {
            'known_faces_dir': self.known_faces_dir,
            'embeddings_file': self.embeddings_file,
            'log_file': self.log_file,
            'backup_dir': self.backup_dir,
            'model': {
                'name': self.model.name,
                'detector_backend': self.model.detector_backend,
                'threshold': self.model.threshold,
                'enforce_detection': self.model.enforce_detection
            },
            'camera': {
                'width': self.camera.width,
                'height': self.camera.height,
                'fps': self.camera.fps,
                'frame_skip': self.camera.frame_skip
            },
            'processing': {
                'max_queue_size': self.processing.max_queue_size,
                'batch_size': self.processing.batch_size,
                'enable_gpu': self.processing.enable_gpu,
                'memory_limit_mb': self.processing.memory_limit_mb,
                'confidence_threshold': self.processing.confidence_threshold,
                'detection_width': self.processing.detection_width,
                'min_analysis_interval': self.processing.min_analysis_interval
            },
            'ui': {
                'window_title': self.ui.window_title,
                'show_fps': self.ui.show_fps,
                'show_confidence': self.ui.show_confidence,
                'font_scale': self.ui.font_scale,
                'font_thickness': self.ui.font_thickness
            }
        }

# Configuração padrão
DEFAULT_CONFIG = SystemConfig()
