"""
Demonstração do Sistema de Múltiplas Câmeras
"""
import os
import sys
import time
import cv2
import numpy as np
from datetime import datetime
import argparse
from typing import List, Dict, Any

# Adicionar o diretório atual ao path
sys.path.append('.')

from multi_camera_system import MultiCameraSystem
from multi_camera_config import get_camera_configs, create_camera_specs, DisplayConfig, ProcessingConfig
from config import DEFAULT_CONFIG
from logger import logger

class MultiCameraDemo:
    """Demonstração do sistema de múltiplas câmeras"""
    
    def __init__(self, config=None):
        self.config = config or DEFAULT_CONFIG
        self.system = None
        self.display_config = DisplayConfig()
        self.processing_config = ProcessingConfig()
        
    def setup_cameras(self, camera_ids: List[str] = None):
        """Configurar câmeras para demonstração"""
        logger.info("Configurando câmeras para demonstração...")
        
        # Obter configurações de câmeras
        camera_configs = get_camera_configs(camera_ids)
        
        if not camera_configs:
            logger.warning("Nenhuma câmera configurada, usando câmera local padrão")
            camera_configs = get_camera_configs(['local_0'])
        
        # Criar sistema
        self.system = MultiCameraSystem(self.config)
        
        # Adicionar câmeras
        for config in camera_configs:
            success = self.system.add_camera(
                config.id, 
                config.type, 
                config.path, 
                config.width, 
                config.height
            )
            if success:
                logger.info(f"✅ Câmera {config.id} adicionada: {config.description}")
            else:
                logger.warning(f"⚠️ Falha ao adicionar câmera {config.id}")
        
        return len([c for c in camera_configs if c.enabled])
    
    def run_demo(self, duration: int = 60):
        """Executar demonstração"""
        logger.info(f"🎬 Iniciando demonstração de {duration} segundos")
        logger.info("=" * 60)
        
        if not self.system:
            logger.error("Sistema não inicializado")
            return
        
        # Inicializar sistema
        if not self.system.initialize_system():
            logger.error("Falha ao inicializar sistema")
            return
        
        # Iniciar processamento
        if not self.system.start_processing():
            logger.error("Falha ao iniciar processamento")
            return
        
        try:
            start_time = time.time()
            frame_count = 0
            
            logger.info("📹 Pressione 'q' para sair a qualquer momento")
            logger.info("=" * 60)
            
            while True:
                current_time = time.time()
                elapsed = current_time - start_time
                
                # Verificar se atingiu duração máxima
                if elapsed >= duration:
                    logger.info(f"⏰ Demonstração concluída após {duration} segundos")
                    break
                
                # Coletar resultados de todas as câmeras
                all_results = []
                
                for camera in self.system.cameras:
                    if not self.system.frame_queues[camera.source_id].empty():
                        frame_data = self.system.frame_queues[camera.source_id].get()
                        result = self.system.process_frame(camera.source_id, frame_data['frame'])
                        all_results.append(result)
                        self.system.total_frames += 1
                        frame_count += 1
                
                # Criar frame de exibição
                if all_results:
                    display_frame = self.system.create_display_frame(
                        all_results, 
                        self.display_config.width, 
                        self.display_config.height
                    )
                    
                    # Adicionar informações da demonstração
                    self._draw_demo_info(display_frame, elapsed, duration)
                    
                    # Exibir frame
                    cv2.imshow('Demonstração - Múltiplas Câmeras', display_frame)
                
                # Verificar tecla de saída
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    logger.info("Demonstração interrompida pelo usuário")
                    break
                
                time.sleep(0.01)
        
        except KeyboardInterrupt:
            logger.info("Demonstração interrompida pelo usuário")
        except Exception as e:
            logger.error(f"Erro na demonstração: {e}")
        finally:
            self._show_final_stats()
            if self.system:
                self.system.stop()
            cv2.destroyAllWindows()
    
    def _draw_demo_info(self, frame: np.ndarray, elapsed: float, duration: int):
        """Desenhar informações da demonstração no frame"""
        # Barra de progresso
        progress = min(elapsed / duration, 1.0)
        bar_width = 200
        bar_height = 20
        bar_x = frame.shape[1] - bar_width - 10
        bar_y = 10
        
        # Fundo da barra
        cv2.rectangle(frame, (bar_x, bar_y), (bar_x + bar_width, bar_y + bar_height), (50, 50, 50), -1)
        
        # Barra de progresso
        progress_width = int(bar_width * progress)
        cv2.rectangle(frame, (bar_x, bar_y), (bar_x + progress_width, bar_y + bar_height), (0, 255, 0), -1)
        
        # Texto de progresso
        progress_text = f"{progress:.1%} ({elapsed:.0f}s/{duration}s)"
        cv2.putText(frame, progress_text, (bar_x, bar_y - 5), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        
        # Estatísticas em tempo real
        stats = self.system.get_system_stats()
        y_offset = frame.shape[0] - 80
        
        cv2.putText(frame, f"FPS: {stats['fps']:.1f}", 
                   (10, y_offset), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(frame, f"Reconhecimentos: {stats['total_recognitions']}", 
                   (10, y_offset + 20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(frame, f"Câmeras Ativas: {stats['active_cameras']}", 
                   (10, y_offset + 40), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        cv2.putText(frame, f"CPU: {stats['cpu_usage']:.1f}% | RAM: {stats['memory_usage']:.1f}%", 
                   (10, y_offset + 60), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    def _show_final_stats(self):
        """Mostrar estatísticas finais"""
        if not self.system:
            return
        
        stats = self.system.get_system_stats()
        
        print("\n" + "=" * 60)
        print("📊 ESTATÍSTICAS FINAIS DA DEMONSTRAÇÃO")
        print("=" * 60)
        print(f"🎯 Total de frames processados: {stats['total_frames']}")
        print(f"✅ Total de reconhecimentos: {stats['total_recognitions']}")
        print(f"📈 FPS médio: {stats['fps']:.2f}")
        print(f"⏱️  Tempo total: {stats['elapsed_time']:.2f}s")
        print(f"📹 Câmeras ativas: {stats['active_cameras']}")
        print(f"💾 Uso de CPU: {stats['cpu_usage']:.1f}%")
        print(f"🧠 Uso de RAM: {stats['memory_usage']:.1f}%")
        print("=" * 60)

def main():
    """Função principal"""
    parser = argparse.ArgumentParser(description='Demonstração do Sistema de Múltiplas Câmeras')
    parser.add_argument('--duration', type=int, default=60, help='Duração da demonstração em segundos')
    parser.add_argument('--cameras', type=str, nargs='+', 
                       help='IDs das câmeras a usar (ex: local_0 rtsp_1)')
    parser.add_argument('--list-cameras', action='store_true', help='Listar câmeras disponíveis')
    
    args = parser.parse_args()
    
    # Listar câmeras disponíveis
    if args.list_cameras:
        from multi_camera_config import CAMERA_CONFIGS
        print("Câmeras Disponíveis:")
        print("=" * 40)
        for cam_id, config in CAMERA_CONFIGS.items():
            status = "✅" if config.enabled else "❌"
            print(f"{status} {cam_id}: {config.description}")
            print(f"   Tipo: {config.type}, Caminho: {config.path}")
        return
    
    # Criar demonstração
    demo = MultiCameraDemo()
    
    # Configurar câmeras
    num_cameras = demo.setup_cameras(args.cameras)
    
    if num_cameras == 0:
        logger.error("Nenhuma câmera disponível para demonstração")
        return
    
    logger.info(f"🎥 {num_cameras} câmera(s) configurada(s) para demonstração")
    
    # Executar demonstração
    demo.run_demo(args.duration)

if __name__ == "__main__":
    main()
