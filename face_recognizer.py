"""
<PERSON><PERSON><PERSON>lo de reconhecimento facial otimizado
"""
import numpy as np
import pickle
import os
from typing import Dict, List, Tuple, Optional, Any
from collections import defaultdict
import time
from logger import logger
from face_detector import FaceEmbeddingExtractor

class FaceRecognizer:
    """Sistema de reconhecimento facial otimizado"""
    
    def __init__(self, 
                 known_faces_dir: str = "known_faces",
                 embeddings_file: str = "face_embeddings.pkl",
                 model_name: str = "Facenet",
                 threshold: float = 0.6):
        
        self.known_faces_dir = known_faces_dir
        self.embeddings_file = embeddings_file
        self.threshold = threshold
        self.model_name = model_name
        
        # Extrator de embeddings
        self.embedding_extractor = FaceEmbeddingExtractor(model_name)
        
        # Armazenamento de embeddings
        self.known_embeddings: Dict[str, np.ndarray] = {}
        self.person_names: List[str] = []
        
        # Cache para melhor performance
        self._similarity_cache: Dict[str, float] = {}
        self._cache_max_size = 1000
        
        # Estatísticas
        self.recognition_stats = {
            'total_recognitions': 0,
            'successful_recognitions': 0,
            'unknown_faces': 0,
            'avg_processing_time': 0.0
        }
        
        # Carregar embeddings existentes
        self.load_embeddings()
    
    def initialize(self):
        """Inicializar reconhecedor de faces"""
        logger.info(f"Reconhecedor inicializado: {self.model_name}")
        return True
    
    def load_embeddings(self) -> bool:
        """Carregar embeddings salvos"""
        if not os.path.exists(self.embeddings_file):
            logger.info("Arquivo de embeddings não encontrado, criando novo")
            return self.create_embeddings()
        
        try:
            with open(self.embeddings_file, 'rb') as f:
                data = pickle.load(f)
                self.known_embeddings = data['embeddings']
                self.person_names = data['names']
                saved_model = data.get('model_name')
                # Validar compatibilidade de modelo
                if saved_model and saved_model != self.model_name:
                    logger.warning(f"Modelo dos embeddings ({saved_model}) difere do atual ({self.model_name}). Recriando embeddings...")
                    return self.create_embeddings()
                # Se não houver model_name salvo, validar por dimensão amostral no primeiro uso
            
            logger.info(f"Embeddings carregados: {len(self.known_embeddings)} pessoas")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao carregar embeddings: {e}")
            return self.create_embeddings()
    
    def create_embeddings(self) -> bool:
        """Criar embeddings das faces conhecidas"""
        if not os.path.exists(self.known_faces_dir):
            logger.error(f"Diretório {self.known_faces_dir} não encontrado")
            return False
        
        logger.info("Criando embeddings das faces conhecidas...")
        
        image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff')
        processed_count = 0
        
        for filename in os.listdir(self.known_faces_dir):
            if filename.lower().endswith(image_extensions):
                person_name = os.path.splitext(filename)[0]
                img_path = os.path.join(self.known_faces_dir, filename)
                
                logger.info(f"Processando: {person_name}")
                
                # Extrair embedding
                embedding = self.embedding_extractor.extract_embedding(img_path)
                
                if embedding is not None:
                    self.known_embeddings[person_name] = embedding
                    self.person_names.append(person_name)
                    processed_count += 1
                else:
                    logger.warning(f"Falha ao processar {person_name}")
        
        # Salvar embeddings
        self.save_embeddings()
        logger.info(f"Embeddings criados para {processed_count} pessoas")
        return processed_count > 0
    
    def save_embeddings(self) -> bool:
        """Salvar embeddings em arquivo"""
        try:
            data = {
                'embeddings': self.known_embeddings,
                'names': self.person_names,
                'created_at': time.time(),
                'model_name': self.embedding_extractor.model_name
            }
            
            with open(self.embeddings_file, 'wb') as f:
                pickle.dump(data, f)
            
            logger.info(f"Embeddings salvos em {self.embeddings_file}")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao salvar embeddings: {e}")
            return False
    
    def calculate_similarity(self, embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """Calcular similaridade coseno entre embeddings"""
        # Normalizar vetores
        embedding1_norm = embedding1 / np.linalg.norm(embedding1)
        embedding2_norm = embedding2 / np.linalg.norm(embedding2)
        
        # Calcular similaridade coseno
        similarity = np.dot(embedding1_norm, embedding2_norm)
        return float(similarity)
    
    def recognize_face(self, face_embedding: np.ndarray) -> Tuple[Optional[str], float, float]:
        """
        Reconhecer uma face
        
        Returns:
            (nome, similaridade, tempo_processamento)
        """
        start_time = time.time()
        
        if not self.known_embeddings:
            processing_time = time.time() - start_time
            return None, 0.0, processing_time
        
        best_match = None
        best_similarity = 0.0
        
        # Verificar compatibilidade de dimensões
        if len(face_embedding) == 0:
            processing_time = time.time() - start_time
            return None, 0.0, processing_time
        
        # Verificar cache primeiro
        embedding_key = str(face_embedding.tobytes()[:100])  # Usar parte do embedding como chave
        
        for person_name, known_embedding in self.known_embeddings.items():
            # Verificar compatibilidade de dimensões
            if len(face_embedding) != len(known_embedding):
                logger.warning(f"Incompatibilidade de dimensões: face_embedding={len(face_embedding)}, known_embedding={len(known_embedding)}")
                # Tentativa única de reconstruir embeddings se incompatibilidade é generalizada
                try:
                    if not hasattr(self, '_tried_rebuild'):
                        self._tried_rebuild = True
                        logger.warning("Reconstruindo embeddings devido à incompatibilidade de dimensões...")
                        if self.create_embeddings():
                            # Recarregar e reiniciar comparação
                            return self.recognize_face(face_embedding)
                except Exception:
                    pass
                continue
            
            cache_key = f"{embedding_key}_{person_name}"
            
            if cache_key in self._similarity_cache:
                similarity = self._similarity_cache[cache_key]
            else:
                try:
                    similarity = self.calculate_similarity(face_embedding, known_embedding)
                    
                    # Adicionar ao cache
                    if len(self._similarity_cache) < self._cache_max_size:
                        self._similarity_cache[cache_key] = similarity
                except Exception as e:
                    logger.error(f"Erro ao calcular similaridade: {e}")
                    continue
            
            if similarity > best_similarity:
                best_similarity = similarity
                best_match = person_name
        
        processing_time = time.time() - start_time
        
        # Atualizar estatísticas
        self.recognition_stats['total_recognitions'] += 1
        if best_similarity >= self.threshold:
            self.recognition_stats['successful_recognitions'] += 1
        else:
            self.recognition_stats['unknown_faces'] += 1
        
        # Atualizar tempo médio de processamento
        total_recognitions = self.recognition_stats['total_recognitions']
        current_avg = self.recognition_stats['avg_processing_time']
        self.recognition_stats['avg_processing_time'] = (
            (current_avg * (total_recognitions - 1) + processing_time) / total_recognitions
        )
        
        # Determinar resultado
        if best_similarity >= self.threshold:
            result_name = best_match
        else:
            result_name = "Desconhecido"
        
        logger.log_recognition(result_name, best_similarity, processing_time)
        
        return result_name, best_similarity, processing_time
    
    def recognize_faces_batch(self, face_embeddings: List[np.ndarray]) -> List[Tuple[Optional[str], float, float]]:
        """Reconhecer múltiplas faces em lote"""
        results = []
        for embedding in face_embeddings:
            result = self.recognize_face(embedding)
            results.append(result)
        return results
    
    def add_person(self, person_name: str, face_image: np.ndarray) -> bool:
        """Adicionar nova pessoa ao sistema"""
        try:
            embedding = self.embedding_extractor.extract_embedding(face_image)
            if embedding is not None:
                self.known_embeddings[person_name] = embedding
                if person_name not in self.person_names:
                    self.person_names.append(person_name)
                
                # Salvar automaticamente
                self.save_embeddings()
                logger.info(f"Pessoa {person_name} adicionada com sucesso")
                return True
            else:
                logger.error(f"Falha ao extrair embedding para {person_name}")
                return False
                
        except Exception as e:
            logger.error(f"Erro ao adicionar pessoa {person_name}: {e}")
            return False
    
    def remove_person(self, person_name: str) -> bool:
        """Remover pessoa do sistema"""
        try:
            if person_name in self.known_embeddings:
                del self.known_embeddings[person_name]
                if person_name in self.person_names:
                    self.person_names.remove(person_name)
                
                # Salvar alterações
                self.save_embeddings()
                logger.info(f"Pessoa {person_name} removida com sucesso")
                return True
            else:
                logger.warning(f"Pessoa {person_name} não encontrada")
                return False
                
        except Exception as e:
            logger.error(f"Erro ao remover pessoa {person_name}: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """Obter estatísticas do sistema"""
        total = self.recognition_stats['total_recognitions']
        success_rate = (
            self.recognition_stats['successful_recognitions'] / total * 100 
            if total > 0 else 0
        )
        
        return {
            'total_recognitions': total,
            'successful_recognitions': self.recognition_stats['successful_recognitions'],
            'unknown_faces': self.recognition_stats['unknown_faces'],
            'success_rate': success_rate,
            'avg_processing_time': self.recognition_stats['avg_processing_time'],
            'known_persons': len(self.known_embeddings),
            'cache_size': len(self._similarity_cache)
        }
    
    def clear_cache(self):
        """Limpar cache de similaridades"""
        self._similarity_cache.clear()
        logger.info("Cache de similaridades limpo")
    
    def reload_embeddings(self) -> bool:
        """Recarregar embeddings do arquivo"""
        logger.info("Recarregando embeddings...")
        return self.load_embeddings()
