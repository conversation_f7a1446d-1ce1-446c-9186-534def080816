import argparse
from pathlib import Path
from typing import List

import cv2
import numpy as np

from src.face.engine import FaceEngine


def parse_args():
    p = argparse.ArgumentParser(description="Constrói galeria de embeddings médios por identidade")
    p.add_argument("--input", type=str, required=True, help="pasta com subpastas por identidade (data/raw_faces)")
    p.add_argument("--output", type=str, default="artifacts/gallery.npz", help="arquivo .npz de saída")
    p.add_argument("--min-per-id", type=int, default=5, help="mínimo de imagens por identidade para entrar na galeria")
    return p.parse_args()


def load_images(folder: Path) -> List[Path]:
    exts = {".jpg", ".jpeg", ".png", ".bmp"}
    return [p for p in folder.glob("*.*") if p.suffix.lower() in exts]


def main():
    args = parse_args()
    in_dir = Path(args.input)
    out_path = Path(args.output)
    out_path.parent.mkdir(parents=True, exist_ok=True)

    engine = FaceEngine(model_pack="buffalo_l", det_size=(640, 640))

    names = []
    embs = []

    for person_dir in sorted([p for p in in_dir.iterdir() if p.is_dir()]):
        person = person_dir.name
        imgs = load_images(person_dir)
        if len(imgs) < args.min_per_id:
            continue
        person_embs = []
        for img_path in imgs:
            img = cv2.imread(str(img_path))
            if img is None:
                continue
            img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            # As imagens foram salvas já alinhadas 112x112; ainda assim detecte/embeba para robustez
            faces = engine.app.get(img)
            if len(faces) == 0:
                continue
            # pega a face principal
            f = faces[0]
            emb = f.embedding.astype(np.float32)
            person_embs.append(emb)
        if len(person_embs) == 0:
            continue
        mean_emb = np.mean(np.stack(person_embs, axis=0), axis=0)
        # normaliza por segurança
        mean_emb = mean_emb / np.linalg.norm(mean_emb)
        names.append(person)
        embs.append(mean_emb)

    if len(names) == 0:
        raise RuntimeError("Nenhuma identidade válida encontrada. Colete mais imagens e tente novamente.")

    names_arr = np.array(names)
    embs_arr = np.stack(embs, axis=0).astype(np.float32)
    np.savez_compressed(str(out_path), names=names_arr, embeddings=embs_arr)
    print(f"Galeria salva em: {out_path} — {len(names)} identidades.")


if __name__ == "__main__":
    main()

