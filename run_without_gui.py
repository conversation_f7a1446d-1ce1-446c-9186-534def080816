"""
Script para executar o sistema sem interface gráfica
Útil para sistemas que não têm tkinter disponível
"""
import sys
import os
import argparse
from datetime import datetime

# Adicionar o diretório atual ao path
sys.path.append('.')

def check_tkinter():
    """Verificar se tkinter está disponível"""
    try:
        import tkinter as tk
        return True
    except ImportError:
        return False

def run_console_version():
    """Executar versão console do sistema"""
    print("🖥️  Executando Sistema de Reconhecimento Facial - Versão Console")
    print("=" * 60)
    
    try:
        from face_recognition_system_v2 import FaceRecognitionSystemV2
        from config import DEFAULT_CONFIG
        
        # Verificar se há faces conhecidas
        if not os.path.exists("known_faces") or len(os.listdir("known_faces")) == 0:
            print("⚠️  Nenhuma face conhecida encontrada!")
            print("📁 Adicione fotos na pasta 'known_faces'")
            print("   Nomeie os arquivos com o nome da pessoa (ex: joao.jpg)")
            return
        
        # Criar sistema
        system = FaceRecognitionSystemV2()
        
        # Verificar se há embeddings
        if len(system.face_recognizer.known_embeddings) == 0:
            print("❌ Falha ao carregar ou criar embeddings")
            return
        
        print(f"✅ Sistema inicializado com {len(system.face_recognizer.known_embeddings)} pessoas conhecidas")
        print("📹 Iniciando reconhecimento...")
        print("   Pressione Ctrl+C para parar")
        
        # Iniciar reconhecimento
        system.start_camera_recognition(camera_index=0)
        
    except KeyboardInterrupt:
        print("\n🛑 Sistema interrompido pelo usuário")
    except Exception as e:
        print(f"❌ Erro: {e}")

def run_demo_version(duration=60):
    """Executar versão demo do sistema"""
    print("🎬 Executando Demonstração do Sistema")
    print("=" * 60)
    
    try:
        from demo_complete import CompleteDemo
        from config import DEFAULT_CONFIG
        
        demo = CompleteDemo()
        demo.run_demo(duration=duration)
        
    except Exception as e:
        print(f"❌ Erro na demonstração: {e}")

def run_multi_camera_demo(duration=60, cameras=None):
    """Executar demonstração de múltiplas câmeras"""
    print("🎥 Executando Demonstração de Múltiplas Câmeras")
    print("=" * 60)
    
    try:
        from demo_multi_camera import MultiCameraDemo
        
        demo = MultiCameraDemo()
        demo.setup_cameras(cameras)
        demo.run_demo(duration=duration)
        
    except Exception as e:
        print(f"❌ Erro na demonstração de múltiplas câmeras: {e}")

def run_api_server():
    """Executar servidor API"""
    print("🌐 Iniciando Servidor API")
    print("=" * 60)
    
    try:
        from api_server import FaceRecognitionAPI
        
        api = FaceRecognitionAPI()
        print("✅ Servidor API iniciado")
        print("📡 Endpoints disponíveis:")
        print("   - http://localhost:5000/api/health")
        print("   - http://localhost:5000/api/recognize")
        print("   - http://localhost:5000/api/attendance")
        print("   - http://localhost:5000/api/analytics")
        print("   - E muitos outros...")
        print("\n   Pressione Ctrl+C para parar o servidor")
        
        api.run(host='0.0.0.0', port=5000, debug=False)
        
    except KeyboardInterrupt:
        print("\n🛑 Servidor API interrompido")
    except Exception as e:
        print(f"❌ Erro no servidor API: {e}")

def run_tests():
    """Executar testes do sistema"""
    print("🧪 Executando Testes do Sistema")
    print("=" * 60)
    
    try:
        import subprocess
        result = subprocess.run([sys.executable, "test_system.py"], 
                              capture_output=True, text=True)
        
        print("Saída dos testes:")
        print(result.stdout)
        
        if result.stderr:
            print("Erros:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ Todos os testes passaram!")
        else:
            print("❌ Alguns testes falharam")
            
    except Exception as e:
        print(f"❌ Erro ao executar testes: {e}")

def show_help():
    """Mostrar ajuda"""
    print("🚀 Sistema de Reconhecimento Facial v2.0")
    print("=" * 60)
    print()
    print("Opções disponíveis:")
    print("  console        - Executar versão console (sem interface gráfica)")
    print("  demo           - Executar demonstração completa")
    print("  multi-camera   - Executar sistema de múltiplas câmeras")
    print("  api            - Iniciar servidor API REST")
    print("  test           - Executar testes do sistema")
    print("  help           - Mostrar esta ajuda")
    print()
    print("Exemplos:")
    print("  python run_without_gui.py console")
    print("  python run_without_gui.py demo --duration 60")
    print("  python run_without_gui.py multi-camera --duration 120")
    print("  python run_without_gui.py api")
    print("  python run_without_gui.py test")
    print()
    print("Para múltiplas câmeras:")
    print("  python run_without_gui.py multi-camera --cameras local_0 rtsp_1")
    print("  python run_without_gui.py multi-camera --list-cameras")

def main():
    """Função principal"""
    parser = argparse.ArgumentParser(description='Sistema de Reconhecimento Facial v2.0')
    parser.add_argument('mode', nargs='?', default='help',
                       choices=['console', 'demo', 'multi-camera', 'api', 'test', 'help'],
                       help='Modo de execução')
    parser.add_argument('--duration', type=int, default=60,
                       help='Duração da demonstração em segundos (apenas para modo demo)')
    parser.add_argument('--cameras', type=str, nargs='+',
                       help='IDs das câmeras para múltiplas câmeras (ex: local_0 rtsp_1)')
    parser.add_argument('--list-cameras', action='store_true',
                       help='Listar câmeras disponíveis (apenas para modo multi-camera)')
    
    args = parser.parse_args()
    
    # Verificar se tkinter está disponível
    has_tkinter = check_tkinter()
    
    if not has_tkinter:
        print("⚠️  Tkinter não está disponível neste sistema")
        print("   A interface gráfica não funcionará")
        print("   Use as opções console, demo ou api")
        print()
    
    # Executar modo solicitado
    if args.mode == 'console':
        run_console_version()
    elif args.mode == 'demo':
        run_demo_version(args.duration)
    elif args.mode == 'multi-camera':
        if args.list_cameras:
            import sys
            sys.argv = ['demo_multi_camera.py', '--list-cameras']
            from demo_multi_camera import main
            main()
        else:
            run_multi_camera_demo(args.duration, args.cameras)
    elif args.mode == 'api':
        run_api_server()
    elif args.mode == 'test':
        run_tests()
    else:
        show_help()

if __name__ == "__main__":
    main()
