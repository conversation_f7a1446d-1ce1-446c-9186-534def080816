import cv2
import numpy as np
from deepface import DeepFace
import os
import pickle
import time
from collections import defaultdict
import threading
import queue

class FaceRecognitionSystem:
    def __init__(self, 
                 known_faces_dir="known_faces", 
                 embeddings_file="face_embeddings.pkl",
                 model_name="VGG-Face",
                 detector_backend="opencv"):
        """
        Sistema de reconhecimento facial em tempo real
        
        Args:
            known_faces_dir: Diretório com fotos das pessoas conhecidas
            embeddings_file: Arquivo para salvar os embeddings
            model_name: Modelo para extração de features (VGG-Face, Facenet, OpenFace, etc.)
            detector_backend: Backend para detecção (opencv, retinaface, mtcnn, etc.)
        """
        self.known_faces_dir = known_faces_dir
        self.embeddings_file = embeddings_file
        self.model_name = model_name
        self.detector_backend = detector_backend
        
        # Dicionário para armazenar embeddings das faces conhecidas
        self.known_embeddings = {}
        self.person_names = []
        
        # Configurações de performance
        self.frame_skip = 3  # Processar apenas a cada N frames
        self.frame_count = 0
        
        # Queue para processamento assíncrono
        self.frame_queue = queue.Queue(maxsize=2)
        self.result_queue = queue.Queue(maxsize=2)
        
        # Thread para processamento
        self.processing = True
        self.process_thread = None
        
        # Criar diretório se não existir
        os.makedirs(known_faces_dir, exist_ok=True)
        
        # Carregar ou criar embeddings
        self.load_or_create_embeddings()
    
    def extract_face_embedding(self, img_path_or_array, person_name=None):
        """
        Extrai embedding de uma face
        
        Args:
            img_path_or_array: Caminho da imagem ou array numpy
            person_name: Nome da pessoa (para logs)
        
        Returns:
            embedding: Vetor de características da face
        """
        try:
            # Extrair embedding usando DeepFace
            embedding_obj = DeepFace.represent(
                img_path=img_path_or_array,
                model_name=self.model_name,
                detector_backend=self.detector_backend,
                enforce_detection=False  # <--- altere para False
            )
            
            # DeepFace retorna uma lista de dicionários
            if embedding_obj and len(embedding_obj) > 0:
                embedding = embedding_obj[0]["embedding"]
                print(f"✅ Embedding extraído para {person_name or 'imagem'}")
                return np.array(embedding)
            else:
                print(f"❌ Nenhuma face detectada em {person_name or 'imagem'}")
                return None
                
        except Exception as e:
            print(f"❌ Erro ao extrair embedding para {person_name or 'imagem'}: {str(e)}")
            return None
    
    def load_or_create_embeddings(self):
        """
        Carrega embeddings existentes ou cria novos a partir das imagens
        """
        # Tentar carregar embeddings salvos
        if os.path.exists(self.embeddings_file):
            try:
                with open(self.embeddings_file, 'rb') as f:
                    data = pickle.load(f)
                    self.known_embeddings = data['embeddings']
                    self.person_names = data['names']
                print(f"✅ Embeddings carregados: {len(self.known_embeddings)} pessoas")
                return
            except Exception as e:
                print(f"⚠️ Erro ao carregar embeddings salvos: {e}")
        
        # Criar embeddings das imagens no diretório
        print("🔄 Criando embeddings das faces conhecidas...")
        
        if not os.path.exists(self.known_faces_dir):
            print(f"❌ Diretório {self.known_faces_dir} não encontrado")
            return
        
        image_extensions = ('.jpg', '.jpeg', '.png', '.bmp', '.tiff')
        
        for filename in os.listdir(self.known_faces_dir):
            if filename.lower().endswith(image_extensions):
                person_name = os.path.splitext(filename)[0]
                img_path = os.path.join(self.known_faces_dir, filename)
                
                print(f"🔄 Processando: {person_name}")
                
                # Extrair embedding
                embedding = self.extract_face_embedding(img_path, person_name)
                
                if embedding is not None:
                    self.known_embeddings[person_name] = embedding
                    self.person_names.append(person_name)
        
        # Salvar embeddings
        self.save_embeddings()
        print(f"✅ Embeddings criados para {len(self.known_embeddings)} pessoas")
    
    def save_embeddings(self):
        """
        Salva os embeddings em arquivo
        """
        try:
            data = {
                'embeddings': self.known_embeddings,
                'names': self.person_names
            }
            with open(self.embeddings_file, 'wb') as f:
                pickle.dump(data, f)
            print(f"✅ Embeddings salvos em {self.embeddings_file}")
        except Exception as e:
            print(f"❌ Erro ao salvar embeddings: {e}")
    
    def calculate_similarity(self, embedding1, embedding2):
        """
        Calcula similaridade coseno entre dois embeddings
        """
        # Normalizar vetores
        embedding1 = embedding1 / np.linalg.norm(embedding1)
        embedding2 = embedding2 / np.linalg.norm(embedding2)
        
        # Calcular similaridade coseno
        similarity = np.dot(embedding1, embedding2)
        return similarity
    
    def recognize_face(self, face_embedding, threshold=0.6):
        """
        Reconhece uma face comparando com embeddings conhecidos
        
        Args:
            face_embedding: Embedding da face a ser reconhecida
            threshold: Limiar de similaridade para reconhecimento
        
        Returns:
            tuple: (nome_da_pessoa, similaridade) ou (None, 0) se não reconhecida
        """
        if not self.known_embeddings:
            return None, 0
        
        best_match = None
        best_similarity = 0
        
        for person_name, known_embedding in self.known_embeddings.items():
            similarity = self.calculate_similarity(face_embedding, known_embedding)
            
            if similarity > best_similarity:
                best_similarity = similarity
                best_match = person_name
        
        # Verificar se a similaridade está acima do limiar
        if best_similarity >= threshold:
            return best_match, best_similarity
        else:
            return "Desconhecido", best_similarity
    
    def process_frame_async(self):
        """
        Processa frames de forma assíncrona
        """
        while self.processing:
            try:
                # Bloquear brevemente aguardando frame
                frame = self.frame_queue.get(timeout=0.2)
                # Detectar e reconhecer faces
                results = self.detect_and_recognize_faces(frame)
                # Colocar resultado na queue
                if not self.result_queue.full():
                    self.result_queue.put(results)
            except queue.Empty:
                continue
            except Exception as e:
                print(f"❌ Erro no processamento assíncrono: {e}")
    
    def detect_and_recognize_faces(self, frame):
        """
        Detecta e reconhece faces em um frame
        
        Returns:
            list: Lista de dicionários com informações das faces detectadas
        """
        results = []
        
        try:
            # Usar DeepFace para detectar faces
            face_objs = DeepFace.extract_faces(
                img_path=frame,
                detector_backend=self.detector_backend,
                enforce_detection=False,
                align=True
            )
            
            if not face_objs:
                return results
            
            # Para cada face detectada
            for i, face_obj in enumerate(face_objs):
                # Converter para formato adequado (0-255)
                face = face_obj.get('face')
                if face is None:
                    continue
                face_array = face
                if face_array.dtype != np.uint8:
                    face_array = (face_array * 255).astype(np.uint8)

                # Extrair área facial (bbox) se disponível
                facial_area = face_obj.get('facial_area') or face_obj.get('region')
                bbox = None
                if isinstance(facial_area, dict):
                    x = int(facial_area.get('x', 0))
                    y = int(facial_area.get('y', 0))
                    w = int(facial_area.get('w', 0) or facial_area.get('width', 0) or 0)
                    h = int(facial_area.get('h', 0) or facial_area.get('height', 0) or 0)
                    bbox = (x, y, w, h)

                # Extrair embedding (pular detecção no DeepFace)
                try:
                    from deepface import DeepFace as _DF  # type: ignore
                    embedding_obj = _DF.represent(
                        img_path=face_array,
                        model_name=self.model_name,
                        detector_backend='skip',
                        enforce_detection=False
                    )
                    face_embedding = np.array(embedding_obj[0]["embedding"]) if embedding_obj else None
                except Exception:
                    face_embedding = self.extract_face_embedding(face_array)

                if face_embedding is not None:
                    # Reconhecer face
                    person_name, similarity = self.recognize_face(face_embedding)

                    results.append({
                        'name': person_name,
                        'similarity': similarity,
                        'face_region': bbox
                    })
        
        except Exception as e:
            print(f"❌ Erro na detecção/reconhecimento: {e}")
        
        return results
    
    def draw_results(self, frame, results):
        """
        Desenha os resultados no frame
        """
        # Desenhar retângulos e nomes baseado nos resultados
        for result in results:
            name = result['name']
            similarity = result['similarity']
            face_region = result.get('face_region')
            
            if face_region is not None:
                x, y, w, h = face_region
                
                # Cor baseada no reconhecimento
                color = (0, 255, 0) if name != "Desconhecido" else (0, 0, 255)
                
                # Desenhar retângulo
                cv2.rectangle(frame, (x, y), (x + w, y + h), color, 2)
                
                # Desenhar nome e similaridade
                label = f"{name} ({similarity:.2f})"
                
                # Calcular posição do texto
                (text_width, text_height), baseline = cv2.getTextSize(
                    label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2
                )
                
                # Desenhar fundo do texto
                cv2.rectangle(
                    frame, 
                    (x, y - text_height - 10), 
                    (x + text_width, y), 
                    color, 
                    -1
                )
                
                # Desenhar texto
                cv2.putText(
                    frame, 
                    label, 
                    (x, y - 5), 
                    cv2.FONT_HERSHEY_SIMPLEX, 
                    0.6, 
                    (255, 255, 255), 
                    2
                )
            else:
                # Fallback: usar detecção OpenCV se não tiver coordenadas
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
                faces = face_cascade.detectMultiScale(gray, 1.1, 4)
                
                # Assumir que temos uma face por resultado (simplificação)
                if len(faces) > 0:
                    x, y, w, h = faces[0]  # Pegar primeira face
                    
                    color = (0, 255, 0) if name != "Desconhecido" else (0, 0, 255)
                    cv2.rectangle(frame, (x, y), (x + w, y + h), color, 2)
                    
                    label = f"{name} ({similarity:.2f})"
                    cv2.putText(frame, label, (x, y - 10), 
                               cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
        
        return frame
    
    def start_camera_recognition(self, camera_index=0):
        """
        Inicia o reconhecimento facial em tempo real
        """
        # Inicializar câmera
        cap = cv2.VideoCapture(camera_index)
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        cap.set(cv2.CAP_PROP_FPS, 30)
        
        if not cap.isOpened():
            print("❌ Erro ao abrir a câmera")
            return
        
        print("✅ Câmera inicializada")
        print("🔄 Iniciando processamento assíncrono...")
        
        # Iniciar thread de processamento
        self.processing = True
        self.process_thread = threading.Thread(target=self.process_frame_async)
        self.process_thread.daemon = True
        self.process_thread.start()
        
        print("📹 Pressione 'q' para sair, 'r' para recarregar embeddings")
        
        last_results = []
        fps_start_time = time.time()
        fps_counter = 0
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    print("❌ Erro ao capturar frame")
                    break
                
                # Flip horizontal para melhor UX
                frame = cv2.flip(frame, 1)
                
                self.frame_count += 1
                fps_counter += 1
                
                # Processar apenas a cada N frames
                if self.frame_count % self.frame_skip == 0:
                    # Adicionar frame à queue se não estiver cheia
                    if not self.frame_queue.full():
                        self.frame_queue.put(frame.copy())
                
                # Pegar resultados se disponíveis
                if not self.result_queue.empty():
                    last_results = self.result_queue.get()
                
                # Desenhar resultados
                frame_with_results = self.draw_results(frame, last_results)
                
                # Calcular e mostrar FPS
                if fps_counter >= 30:
                    fps = fps_counter / (time.time() - fps_start_time)
                    fps_start_time = time.time()
                    fps_counter = 0
                    
                    cv2.putText(frame_with_results, f"FPS: {fps:.1f}", (10, 30),
                               cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 255), 2)
                
                # Mostrar informações do sistema
                info_text = f"Pessoas conhecidas: {len(self.known_embeddings)}"
                cv2.putText(frame_with_results, info_text, (10, frame.shape[0] - 10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
                
                # Exibir frame
                cv2.imshow('Reconhecimento Facial - DeepFace', frame_with_results)
                
                # Controles de teclado
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    print("🛑 Encerrando...")
                    break
                elif key == ord('r'):
                    print("🔄 Recarregando embeddings...")
                    self.load_or_create_embeddings()
                elif key == ord('s'):
                    # Salvar frame atual
                    cv2.imwrite(f'capture_{int(time.time())}.jpg', frame)
                    print("📸 Frame salvo!")
        
        except KeyboardInterrupt:
            print("🛑 Interrompido pelo usuário")
        
        finally:
            # Cleanup
            self.processing = False
            if self.process_thread:
                self.process_thread.join(timeout=2)
            
            cap.release()
            cv2.destroyAllWindows()
            print("✅ Recursos liberados")

def main():
    """
    Função principal
    """
    print("🚀 Sistema de Reconhecimento Facial com DeepFace")
    print("=" * 50)
    
    # Configurar sistema
    system = FaceRecognitionSystem(
        known_faces_dir="known_faces",
        embeddings_file="face_embeddings.pkl",
        model_name="DeepFace",  # Opções: VGG-Face, Facenet, OpenFace, DeepFace, etc.
        detector_backend="opencv"  # Opções: opencv, retinaface, mtcnn, etc.
    )
    
    if len(system.known_embeddings) == 0:
        print("⚠️ Nenhuma face conhecida encontrada!")
        print(f"📁 Adicione fotos na pasta '{system.known_faces_dir}'")
        print("   Nomeie os arquivos com o nome da pessoa (ex: joao.jpg)")
        return
    
    # Iniciar reconhecimento
    system.start_camera_recognition(camera_index=0)

if __name__ == "__main__":
    main()
