"""
Sistema de Reconhecimento Facial com Múltiplas Câmeras
Suporte para câmeras locais e RTSP com divisão horizontal
"""
import cv2
import numpy as np
import threading
import queue
import time
import os
import psutil
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import argparse

from config import SystemConfig, DEFAULT_CONFIG
from logger import logger
from face_detector import FaceDetector
from face_recognizer import FaceRecognizer
from advanced_features import (
    EmotionDetector, AgeGenderDetector, AttendanceTracker, 
    NotificationSystem, VideoRecorder, AdvancedAnalytics
)

class CameraSource:
    """Classe para gerenciar uma fonte de câmera"""
    
    def __init__(self, source_id: str, source_type: str, source_path: str, 
                 width: int = 640, height: int = 480, capture_backend: Optional[str] = None):
        self.source_id = source_id
        self.source_type = source_type  # 'local' ou 'rtsp'
        self.source_path = source_path
        self.width = width
        self.height = height
        self.cap = None
        self.capture_backend = capture_backend
        self.is_active = False
        self.last_frame = None
        self.fps = 0
        self.frame_count = 0
        self.start_time = time.time()
        
    def initialize(self) -> bool:
        """Inicializar a câmera"""
        try:
            if self.source_type == 'local':
                # Preferir backend de baixa latência no Windows quando disponível
                try:
                    self.cap = cv2.VideoCapture(int(self.source_path), cv2.CAP_DSHOW)
                except Exception:
                    self.cap = cv2.VideoCapture(int(self.source_path))
            else:  # rtsp
                self.cap = cv2.VideoCapture(self.source_path)
                
            if not self.cap.isOpened():
                logger.error(f"Erro ao abrir câmera {self.source_id}: {self.source_path}")
                return False
                
            # Configurar propriedades da câmera
            self.cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.width)
            self.cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.height)
            # Tentar limitar/estabilizar FPS conforme configuração
            try:
                self.cap.set(cv2.CAP_PROP_FPS, 30)
            except Exception:
                pass
            # Reduzir buffer para diminuir latência (principalmente RTSP)
            try:
                self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
            except Exception:
                pass
            
            self.is_active = True
            logger.info(f"Câmera {self.source_id} inicializada: {self.source_type} - {self.source_path}")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao inicializar câmera {self.source_id}: {e}")
            return False
    
    def read_frame(self) -> Optional[np.ndarray]:
        """Ler frame da câmera"""
        if not self.is_active or self.cap is None:
            return None
            
        try:
            ret, frame = self.cap.read()
            if ret:
                self.last_frame = frame
                self.frame_count += 1
                
                # Calcular FPS
                elapsed = time.time() - self.start_time
                if elapsed > 0:
                    self.fps = self.frame_count / elapsed
                    
                return frame
            else:
                logger.warning(f"Falha ao ler frame da câmera {self.source_id}")
                return None
                
        except Exception as e:
            logger.error(f"Erro ao ler frame da câmera {self.source_id}: {e}")
            return None
    
    def release(self):
        """Liberar recursos da câmera"""
        if self.cap is not None:
            self.cap.release()
            self.is_active = False
            logger.info(f"Câmera {self.source_id} liberada")

class MultiCameraSystem:
    """Sistema principal de múltiplas câmeras"""
    
    def __init__(self, config: SystemConfig):
        self.config = config
        self.cameras: List[CameraSource] = []
        # Detecção com limiar mais permissivo para garantir captura de faces
        self.face_detector = FaceDetector(config.model.detector_backend, confidence_threshold=0.3)
        # Corrigir parâmetros: usar diretórios/arquivos corretos e threshold do config
        self.face_recognizer = FaceRecognizer(
            known_faces_dir=config.known_faces_dir,
            embeddings_file=config.embeddings_file,
            model_name=config.model.name,
            threshold=config.model.threshold,
        )
        
        # Funcionalidades avançadas
        self.emotion_detector = EmotionDetector()
        self.age_gender_detector = AgeGenderDetector()
        self.attendance_tracker = AttendanceTracker()
        self.notification_system = NotificationSystem()
        self.video_recorder = VideoRecorder()
        self.analytics = AdvancedAnalytics()
        
        # Flags de processamento dinâmico (para toggles na GUI)
        self.enable_emotion = True
        self.enable_age_gender = True
        
        # Threading
        self.processing_threads = []
        self.frame_queues = {}
        self._frame_index: Dict[str, int] = {}
        self._last_analysis_ts: Dict[str, float] = {}
        self._last_faces: Dict[str, List[Dict[str, Any]]] = {}
        # Suavização de bboxes por câmera
        self._smooth_state: Dict[str, List[Dict[str, Any]]] = {}
        self.running = False
        
        # Estatísticas
        self.total_frames = 0
        self.total_recognitions = 0
        self.start_time = time.time()
        
    def add_camera(self, source_id: str, source_type: str, source_path: str, 
                   width: int = 640, height: int = 480) -> bool:
        """Adicionar uma câmera ao sistema"""
        camera = CameraSource(source_id, source_type, source_path, width, height)
        
        if camera.initialize():
            self.cameras.append(camera)
            # Fila pequena para manter vídeo fluido (sempre manter frames mais novos)
            max_q = max(1, getattr(self.config.processing, 'max_queue_size', 3))
            self.frame_queues[source_id] = queue.Queue(maxsize=max_q)
            self._frame_index[source_id] = 0
            logger.info(f"Câmera {source_id} adicionada com sucesso")
            return True
        else:
            logger.error(f"Falha ao adicionar câmera {source_id}")
            return False
    
    def initialize_system(self):
        """Inicializar sistema de reconhecimento"""
        try:
            logger.info("Inicializando sistema de reconhecimento facial...")
            try:
                cv2.setUseOptimized(True)
            except Exception:
                pass
            
            # Inicializar detector de faces
            self.face_detector.initialize()
            
            # Inicializar reconhecedor
            self.face_recognizer.initialize()
            
            # Inicializar funcionalidades avançadas
            self.emotion_detector.initialize()
            self.age_gender_detector.initialize()
            self.attendance_tracker.initialize()
            self.notification_system.initialize()
            self.video_recorder.initialize()
            self.analytics.initialize()
            
            logger.info("Sistema inicializado com sucesso")
            return True
            
        except Exception as e:
            logger.error(f"Erro ao inicializar sistema: {e}")
            return False
    
    def start_processing(self):
        """Iniciar processamento das câmeras"""
        if not self.cameras:
            logger.error("Nenhuma câmera configurada")
            return False
            
        self.running = True
        
        # Criar thread para cada câmera
        for camera in self.cameras:
            thread = threading.Thread(
                target=self._camera_processing_loop,
                args=(camera,),
                daemon=True
            )
            thread.start()
            self.processing_threads.append(thread)
            
        logger.info(f"Processamento iniciado para {len(self.cameras)} câmeras")
        return True
    
    def _camera_processing_loop(self, camera: CameraSource):
        """Loop de processamento para uma câmera"""
        logger.info(f"Iniciando processamento da câmera {camera.source_id}")
        
        while self.running and camera.is_active:
            try:
                frame = camera.read_frame()
                if frame is None:
                    time.sleep(0.1)
                    continue
                
                # Adicionar frame à fila de processamento
                q = self.frame_queues[camera.source_id]
                if q.full():
                    # Descartar o frame mais antigo para manter baixa latência
                    try:
                        q.get_nowait()
                    except Exception:
                        pass
                q.put({
                    'camera_id': camera.source_id,
                    'frame': frame,
                    'timestamp': time.time()
                })
                
                # Controle de FPS
                # Para RTSP normalmente não dormimos; local respeita fps alvo
                if camera.source_type == 'local':
                    time.sleep(max(0.0, 1.0 / max(1, self.config.camera.fps)))
            
            except Exception as e:
                logger.error(f"Erro no processamento da câmera {camera.source_id}: {e}")
                time.sleep(1)
        
        logger.info(f"Processamento da câmera {camera.source_id} finalizado")
    
    def process_frame(self, camera_id: str, frame: np.ndarray) -> Dict[str, Any]:
        """Processar um frame de uma câmera específica"""
        try:
            # Reduzir carga: processar somente a cada N frames e respeitar intervalo mínimo
            idx = (self._frame_index.get(camera_id, 0) + 1)
            self._frame_index[camera_id] = idx
            analysis_stride = max(1, getattr(self.config.camera, 'frame_skip', 2))
            do_by_stride = (idx % analysis_stride) == 0
            last_ts = self._last_analysis_ts.get(camera_id, 0.0)
            min_interval = max(0.05, getattr(self.config.processing, 'min_analysis_interval', 0.2))
            do_by_time = (time.time() - last_ts) >= min_interval
            do_analysis = do_by_stride and do_by_time

            if not do_analysis:
                # Retornar rápido, mantendo últimas faces para overlay
                return {
                    'camera_id': camera_id,
                    'faces': self._last_faces.get(camera_id, []),
                    'frame': frame,
                    'timestamp': time.time()
                }

            # Detecção em resolução reduzida, mantendo proporção
            h, w = frame.shape[:2]
            target_w = max(160, min(getattr(self.config.processing, 'detection_width', 480), w))
            scale = target_w / float(w)
            if scale < 1.0:
                resized = cv2.resize(frame, (int(w * scale), int(h * scale)), interpolation=cv2.INTER_AREA)
            else:
                resized = frame
            faces_small = self.face_detector.detect_faces(resized)
            # Reescalar bboxes para o tamanho original
            faces = []
            for f in faces_small:
                if 'bbox' in f:
                    sx, sy, sw, sh = f['bbox']
                    x = int(sx / scale)
                    y = int(sy / scale)
                    bw = int(sw / scale)
                    bh = int(sh / scale)
                    f2 = dict(f)
                    f2['bbox'] = (x, y, bw, bh)
                    # Reescalar landmarks, se houver
                    lmk = f.get('landmarks')
                    if lmk:
                        try:
                            f2['landmarks'] = self._rescale_landmarks(lmk, 1.0 / scale)
                        except Exception:
                            f2['landmarks'] = lmk
                    faces.append(f2)
                else:
                    faces.append(f)

            # Suavizar bboxes para reduzir tremor
            faces = self._smooth_faces(camera_id, faces)
            
            results = {
                'camera_id': camera_id,
                'faces': [],
                'frame': frame,
                'timestamp': time.time()
            }
            
            for face in faces:
                # Extrair região da face do frame usando bbox
                if 'bbox' in face:
                    x, y, bw, bh = face['bbox']
                    # Garantir limites válidos
                    x0 = max(0, int(x)); y0 = max(0, int(y))
                    x1 = min(frame.shape[1], int(x + bw)); y1 = min(frame.shape[0], int(y + bh))
                    if x1 <= x0 or y1 <= y0:
                        continue
                    face_region = frame[y0:y1, x0:x1]
                    
                    # Usar face_array se disponível, senão usar face_region
                    face_array = face.get('face_array', face_region)
                    
                    # Extrair embedding
                    embedding = self.face_recognizer.embedding_extractor.extract_embedding(face_array)
                    if embedding is None:
                        continue
                    
                    # Reconhecer face
                    name, confidence, processing_time = self.face_recognizer.recognize_face(embedding)
                    
                    # Detectar emoção (opcional)
                    emotion = None
                    if self.enable_emotion:
                        emotion = self.emotion_detector.detect_emotion(face_region)
                    
                    # Detectar idade e gênero (opcional)
                    age_gender_data = {}
                    age = 0
                    gender = 'Unknown'
                    if self.enable_age_gender:
                        age_gender_data = self.age_gender_detector.detect_age_gender(face_region)
                        age = age_gender_data.get('age', 0)
                        gender = age_gender_data.get('gender', 'Unknown')
                else:
                    continue
                
                # Registrar presença
                if name and confidence > self.config.processing.confidence_threshold:
                    self.attendance_tracker.record_presence(name, confidence)
                    self.notification_system.add_notification(
                        'recognition', f"{name} reconhecido (confiança: {confidence:.2f})"
                    )
                
                # Gravar vídeo se alta confiança
                if name and confidence > 0.8:
                    self.video_recorder.record_frame(frame, name, confidence)
                
                # Coletar analytics
                self.analytics.record_recognition(name, confidence, emotion if isinstance(emotion, str) else (emotion or {}).get('emotion'), age, gender)
                
                face_result = {
                    'name': name,
                    'confidence': confidence,
                    'emotion': emotion,
                    'age': age,
                    'gender': gender,
                    'bbox': (x0, y0, x1 - x0, y1 - y0),
                    'landmarks': face.get('landmarks'),
                    'processing_time': processing_time
                }
                
                results['faces'].append(face_result)
                self.total_recognitions += 1
            
            # Guardar últimas faces e timestamp de análise
            self._last_faces[camera_id] = results['faces']
            self._last_analysis_ts[camera_id] = time.time()
            return results
            
        except Exception as e:
            logger.error(f"Erro ao processar frame da câmera {camera_id}: {e}")
            return {'camera_id': camera_id, 'faces': [], 'frame': frame, 'error': str(e)}
    
    def create_display_frame(self, results: List[Dict[str, Any]], 
                           display_width: int = 1280, display_height: int = 480) -> np.ndarray:
        """Criar frame de exibição com divisão horizontal preservando proporção (letterbox por câmera)."""
        if not results:
            return np.zeros((display_height, display_width, 3), dtype=np.uint8)

        num_cameras = max(1, len(results))
        camera_width = max(1, display_width // num_cameras)

        # Canvas final
        display_frame = np.zeros((display_height, display_width, 3), dtype=np.uint8)

        for i, result in enumerate(results):
            # Área alvo (coluna) para esta câmera
            x_start = i * camera_width
            x_end = min(display_width, x_start + camera_width)

            # Criar painel vazio para esta câmera
            panel_w = x_end - x_start
            panel_h = display_height
            panel = np.zeros((panel_h, panel_w, 3), dtype=np.uint8)

            if 'error' in result:
                # Exibir mensagem de erro centralizada
                cv2.putText(panel, f"Erro: {result['error']}", (10, 30),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
                cv2.putText(panel, f"Camera: {result.get('camera_id','?')}", (10, 60),
                            cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
            else:
                frame = result.get('frame')
                if frame is None:
                    frame = np.zeros((panel_h, panel_w, 3), dtype=np.uint8)

                h0, w0 = frame.shape[:2]
                # Calcular dimensões mantendo proporção
                aspect = w0 / max(1.0, float(h0))
                container_aspect = panel_w / max(1.0, float(panel_h))
                if aspect >= container_aspect:
                    # Ajustar por largura
                    new_w = panel_w
                    new_h = int(round(new_w / max(1e-6, aspect)))
                else:
                    # Ajustar por altura
                    new_h = panel_h
                    new_w = int(round(new_h * aspect))

                # Redimensionar com interpolação adequada
                interp = cv2.INTER_AREA if (new_w < w0 or new_h < h0) else cv2.INTER_LINEAR
                resized = cv2.resize(frame, (max(1, new_w), max(1, new_h)), interpolation=interp)

                # Centralizar (letterbox)
                x_off = (panel_w - resized.shape[1]) // 2
                y_off = (panel_h - resized.shape[0]) // 2
                panel[y_off:y_off + resized.shape[0], x_off:x_off + resized.shape[1]] = resized

                # Desenhar overlays com escala/offset corretos
                try:
                    scale_x = resized.shape[1] / max(1.0, float(w0))
                    scale_y = resized.shape[0] / max(1.0, float(h0))
                    self._draw_camera_info(panel, result, panel_w,
                                           scale_x=scale_x, scale_y=scale_y,
                                           x_offset=x_off, y_offset=y_off)
                except Exception:
                    pass

            # Copiar painel para a coluna do canvas final
            display_frame[:, x_start:x_end] = panel

        return display_frame

    def create_display_frame_vertical(self, results: List[Dict[str, Any]], 
                                      display_width: int = 800, display_height: int = 600) -> np.ndarray:
        """Criar frame de exibição com divisão vertical (uma acima da outra)"""
        if not results:
            return np.zeros((display_height, display_width, 3), dtype=np.uint8)

        num_cameras = max(1, len(results))
        camera_height = display_height // num_cameras

        display_frame = np.zeros((display_height, display_width, 3), dtype=np.uint8)

        for i, result in enumerate(results):
            # Obter frame original
            frame = result.get('frame')
            if frame is None:
                frame = np.zeros((camera_height, display_width, 3), dtype=np.uint8)
            
            # Manter proporção original ao redimensionar
            h, w = frame.shape[:2]
            aspect_ratio = w / h
            
            # Calcular dimensões mantendo proporção
            if aspect_ratio > display_width / camera_height:
                # Frame é mais largo - ajustar largura
                new_width = display_width
                new_height = int(display_width / aspect_ratio)
            else:
                # Frame é mais alto - ajustar altura
                new_height = camera_height
                new_width = int(camera_height * aspect_ratio)
            
            # Redimensionar mantendo proporção (melhor para downscale)
            interp = cv2.INTER_AREA if new_width < w or new_height < h else cv2.INTER_LINEAR
            frame_resized = cv2.resize(frame, (new_width, new_height), interpolation=interp)
            
            # Centralizar no espaço disponível
            y_offset = (camera_height - new_height) // 2
            x_offset = (display_width - new_width) // 2
            
            # Criar frame com fundo preto
            camera_frame = np.zeros((camera_height, display_width, 3), dtype=np.uint8)
            camera_frame[y_offset:y_offset+new_height, x_offset:x_offset+new_width] = frame_resized
            
            # Desenhar informações da câmera e faces com escala/offset
            try:
                scale_x = new_width / max(1.0, w)
                scale_y = new_height / max(1.0, h)
                self._draw_camera_info(camera_frame, result, display_width, scale_x=scale_x, scale_y=scale_y, x_offset=x_offset, y_offset=y_offset)
            except Exception:
                pass

            # Posicionar no canvas
            y_start = i * camera_height
            y_end = y_start + camera_height
            display_frame[y_start:y_end, :] = camera_frame

        return display_frame
    
    def _draw_camera_info(self, frame: np.ndarray, result: Dict[str, Any], width: int,
                           scale_x: float = 1.0, scale_y: float = 1.0, x_offset: int = 0, y_offset: int = 0):
        """Desenhar informações da câmera no frame com ajuste de escala/offset para bboxes e landmarks."""
        camera_id = result['camera_id']
        faces = result['faces']
        
        # Cabeçalho da câmera
        cv2.rectangle(frame, (0, 0), (width, 40), (0, 0, 0), -1)
        cv2.putText(frame, f"Camera: {camera_id}", (10, 25), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
        
        # Desenhar faces
        for face in faces:
            bbox = face['bbox']
            name = face['name'] or 'Desconhecido'
            confidence = face['confidence']
            emotion = face['emotion']
            
            # Cor baseada na confiança
            color = (0, 255, 0) if confidence > 0.7 else (0, 255, 255) if confidence > 0.5 else (0, 0, 255)
            
            # Desenhar retângulo
            x, y, w, h = bbox
            sx = int(x * scale_x) + x_offset
            sy = int(y * scale_y) + y_offset
            sw = int(w * scale_x)
            sh = int(h * scale_y)
            cv2.rectangle(frame, (sx, sy), (sx + sw, sy + sh), color, 2)
            
            # Desenhar informações
            info_text = f"{name}: {confidence:.2f}"
            cv2.putText(frame, info_text, (sx, max(15, sy - 10)), 
                        cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
            
            if emotion:
                emotion_text = f"Emoção: {emotion}"
                cv2.putText(frame, emotion_text, (sx, sy + sh + 15), 
                            cv2.FONT_HERSHEY_SIMPLEX, 0.4, (255, 255, 255), 1)

            # Desenhar landmarks, se presentes
            lmk = face.get('landmarks')
            if isinstance(lmk, dict):
                for key, pt in lmk.items():
                    try:
                        px, py = pt
                        px = int(px * scale_x) + x_offset
                        py = int(py * scale_y) + y_offset
                        cv2.circle(frame, (px, py), 2, (255, 255, 0), -1)
                    except Exception:
                        continue

    # ------------------
    # Utilitários
    # ------------------
    def _rescale_landmarks(self, landmarks: Any, scale_factor: float) -> Any:
        """Reescalar landmarks por um fator (para converter do resized para original)."""
        if isinstance(landmarks, dict):
            out = {}
            for k, v in landmarks.items():
                try:
                    x, y = v
                    out[k] = (float(x) * scale_factor, float(y) * scale_factor)
                except Exception:
                    out[k] = v
            return out
        return landmarks

    def _smooth_faces(self, camera_id: str, faces: List[Dict[str, Any]], alpha: float = 0.6) -> List[Dict[str, Any]]:
        """Aplicar suavização por média móvel exponencial nas bboxes, associando por proximidade do centro."""
        prev = self._smooth_state.get(camera_id, [])
        used_prev = set()
        smoothed: List[Dict[str, Any]] = []

        def center(b):
            x, y, w, h = b
            return (x + w / 2.0, y + h / 2.0)

        for f in faces:
            b = f.get('bbox')
            if not b:
                smoothed.append(f)
                continue
            cx, cy = center(b)
            # Encontrar bbox anterior mais próxima
            best_idx = -1
            best_dist = 1e9
            for i, p in enumerate(prev):
                if i in used_prev:
                    continue
                pb = p.get('bbox')
                if not pb:
                    continue
                pcx, pcy = center(pb)
                d = (pcx - cx) ** 2 + (pcy - cy) ** 2
                if d < best_dist:
                    best_dist = d
                    best_idx = i
            if best_idx >= 0:
                pb = prev[best_idx]['bbox']
                px, py, pw, ph = [float(v) for v in pb]
                x, y, w, h = [float(v) for v in b]
                sx = alpha * x + (1 - alpha) * px
                sy = alpha * y + (1 - alpha) * py
                sw = alpha * w + (1 - alpha) * pw
                sh = alpha * h + (1 - alpha) * ph
                f2 = dict(f)
                f2['bbox'] = (int(sx), int(sy), int(sw), int(sh))
                smoothed.append(f2)
                used_prev.add(best_idx)
            else:
                smoothed.append(f)

        # Atualizar estado
        self._smooth_state[camera_id] = smoothed
        return smoothed
    
    def get_system_stats(self) -> Dict[str, Any]:
        """Obter estatísticas do sistema"""
        elapsed = time.time() - self.start_time
        fps = self.total_frames / elapsed if elapsed > 0 else 0
        
        return {
            'total_frames': self.total_frames,
            'total_recognitions': self.total_recognitions,
            'fps': fps,
            'elapsed_time': elapsed,
            'active_cameras': len([c for c in self.cameras if c.is_active]),
            'memory_usage': psutil.virtual_memory().percent,
            'cpu_usage': psutil.cpu_percent()
        }
    
    def stop(self):
        """Parar o sistema"""
        logger.info("Parando sistema de múltiplas câmeras...")
        
        self.running = False
        
        # Parar threads de processamento
        for thread in self.processing_threads:
            thread.join(timeout=2)
        
        # Liberar câmeras
        for camera in self.cameras:
            camera.release()
        
        # Finalizar funcionalidades avançadas
        self.video_recorder.finalize()
        self.analytics.finalize()
        
        logger.info("Sistema parado")

def main():
    """Função principal"""
    parser = argparse.ArgumentParser(description='Sistema de Reconhecimento Facial com Múltiplas Câmeras')
    parser.add_argument('--config', type=str, default='config.py', help='Arquivo de configuração')
    parser.add_argument('--cameras', type=str, nargs='+', 
                       help='Lista de câmeras no formato: id:tipo:caminho (ex: cam1:local:0 cam2:rtsp:rtsp://url)')
    
    args = parser.parse_args()
    
    # Carregar configuração
    config = DEFAULT_CONFIG
    
    # Criar sistema
    system = MultiCameraSystem(config)
    
    # Adicionar câmeras padrão se não especificadas
    if not args.cameras:
        # Câmera local padrão
        system.add_camera('local', 'local', '0', 640, 480)
        logger.info("Usando câmera local padrão (índice 0)")
    else:
        # Adicionar câmeras especificadas
        for camera_spec in args.cameras:
            try:
                parts = camera_spec.split(':')
                if len(parts) != 3:
                    logger.error(f"Formato inválido para câmera: {camera_spec}")
                    continue
                
                camera_id, camera_type, camera_path = parts
                system.add_camera(camera_id, camera_type, camera_path, 640, 480)
                
            except Exception as e:
                logger.error(f"Erro ao adicionar câmera {camera_spec}: {e}")
    
    # Inicializar sistema
    if not system.initialize_system():
        logger.error("Falha ao inicializar sistema")
        return
    
    # Iniciar processamento
    if not system.start_processing():
        logger.error("Falha ao iniciar processamento")
        return
    
    try:
        logger.info("Sistema de múltiplas câmeras iniciado")
        logger.info("Pressione 'q' para sair")
        
        # Loop principal de exibição
        while True:
            # Coletar resultados de todas as câmeras
            all_results = []
            
            for camera in system.cameras:
                if not system.frame_queues[camera.source_id].empty():
                    frame_data = system.frame_queues[camera.source_id].get()
                    result = system.process_frame(camera.source_id, frame_data['frame'])
                    all_results.append(result)
                    system.total_frames += 1
            
            # Criar frame de exibição
            if all_results:
                display_frame = system.create_display_frame(all_results)
                
                # Mostrar estatísticas
                stats = system.get_system_stats()
                cv2.putText(display_frame, f"FPS: {stats['fps']:.1f}", 
                           (10, display_frame.shape[0] - 60), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                cv2.putText(display_frame, f"Reconhecimentos: {stats['total_recognitions']}", 
                           (10, display_frame.shape[0] - 40), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                cv2.putText(display_frame, f"Câmeras: {stats['active_cameras']}", 
                           (10, display_frame.shape[0] - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
                
                # Exibir frame
                cv2.imshow('Sistema de Múltiplas Câmeras', display_frame)
            
            # Verificar tecla de saída
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            
            time.sleep(0.01)
    
    except KeyboardInterrupt:
        logger.info("Sistema interrompido pelo usuário")
    except Exception as e:
        logger.error(f"Erro no sistema: {e}")
    finally:
        system.stop()
        cv2.destroyAllWindows()

if __name__ == "__main__":
    main()
