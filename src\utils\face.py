from __future__ import annotations

import numpy as np


def l2_normalize(x: np.ndarray, axis: int = 1, eps: float = 1e-10) -> np.ndarray:
    norm = np.linalg.norm(x, ord=2, axis=axis, keepdims=True)
    return x / np.clip(norm, eps, None)


def cosine_similarity(a: np.ndarray, b: np.ndarray) -> float:
    """Cosine similarity between 1D vectors a and b (assumed L2-normalized)."""
    return float(np.dot(a, b))


def cosine_sim_matrix(A: np.ndarray, B: np.ndarray) -> np.ndarray:
    """Cosine similarity between rows of A (Nxd) and B (Mxd). Assumes L2-normalized."""
    return A @ B.T

