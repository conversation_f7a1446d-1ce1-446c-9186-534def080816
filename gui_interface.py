"""
Interface gráfica moderna para o sistema de reconhecimento facial
"""
import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import cv2
import numpy as np
from PIL import Image, ImageTk
import threading
import time
from datetime import datetime
import json
import os
from typing import Optional, Dict, Any, List, TYPE_CHECKING

from config import DEFAULT_CONFIG
if TYPE_CHECKING:
    from config import SystemConfig
from face_recognition_system_v2 import FaceRecognitionSystemV2
from multi_camera_system import MultiCameraSystem
from advanced_features import (
    EmotionDetector, AgeGenderDetector, AttendanceTracker, 
    NotificationSystem, VideoRecorder, AdvancedAnalytics
)
from logger import logger

class ModernFaceRecognitionGUI:
    """Interface gráfica moderna para reconhecimento facial"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("Sistema de Reconhecimento Facial - Interface Moderna")
        self.root.geometry("1400x900")
        self.root.configure(bg='#2c3e50')
        # Escala de UI padrão (ajuda em telas 4K/alta densidade)
        # Pode ser ajustada via menu Visualização → Zoom ou atalhos.
        try:
            self.set_ui_scale(1.25)
            # Permitir sobrescrever via variável de ambiente (GUI_SCALE ou UI_SCALE)
            env_scale = os.getenv('GUI_SCALE') or os.getenv('UI_SCALE')
            if env_scale:
                self.set_ui_scale(float(env_scale))
        except Exception:
            pass
        
        # Configuração do sistema
        self.config: 'SystemConfig' = DEFAULT_CONFIG
        self.system = None
        self.multi_system = None
        self.is_running = False
        self.current_frame = None
        
        # Estado das câmeras individuais
        self.camera_states = {
            'Webcam': {'running': False, 'thread': None},
            'C100': {'running': False, 'thread': None}
        }
        
        # Componentes avançados
        self.emotion_detector = EmotionDetector()
        self.age_gender_detector = AgeGenderDetector()
        self.attendance_tracker = AttendanceTracker()
        self.notification_system = NotificationSystem()
        self.video_recorder = VideoRecorder()
        self.analytics = AdvancedAnalytics()
        
        # Variáveis de controle
        self.camera_thread = None
        self.multi_camera_thread = None
        self.last_recognition_time = {}
        self.recognition_cooldown = 2  # Segundos entre reconhecimentos da mesma pessoa
        # Controle de análise/desenho para fluidez (analisar 1 a cada N frames por câmera)
        self._ui_cam_stride = 3
        self._ui_cam_counter: Dict[str, int] = {}
        # Modo Performance (toggle)
        self.performance_mode_var = tk.BooleanVar(value=False)
        self._saved_backend = self.config.model.detector_backend
        self._saved_frame_skip = self.config.camera.frame_skip
        # Buffer de renderização (double buffer)
        self._photo_front = None
        self._photo_back = None
        self._photo_toggle = False
        # Cache da última exibição por câmera (para não alternar painéis)
        self._last_display: Dict[str, Dict[str, Any]] = {}
        
        # Estado de UI
        self.is_fullscreen = False
        self._ui_scale = 1.0

        # Dados de UI
        self.recognition_events: List[Dict[str, Any]] = []  # buffer p/ filtro
        # Estride de análise para câmera única
        self._single_cam_stride = 2
        self._single_cam_counter = 0

        # Configurar interface
        self.setup_styles()
        self.create_widgets()
        self.setup_layout()
        # Aplicar tema ativo para harmonizar cores dos widgets existentes
        try:
            self.apply_theme(self.active_theme)
        except Exception:
            pass
        # Ajuste tardio do sash após a primeira renderização
        try:
            self.root.after(800, self._post_init_layout_fix)
        except Exception:
            pass

        # Inicializar sistema
        self.initialize_system()

        # Encerramento limpo
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)

    class Tooltip:
        """Tooltip simples para widgets Tkinter/ttk."""
        def __init__(self, widget, text: str, bg: str = '#111827', fg: str = '#e5e7eb'):
            self.widget = widget
            self.text = text
            self.tipwindow = None
            self.bg = bg
            self.fg = fg
            widget.bind('<Enter>', self.show)
            widget.bind('<Leave>', self.hide)

        def show(self, event=None):
            if self.tipwindow or not self.text:
                return
            x = self.widget.winfo_rootx() + 10
            y = self.widget.winfo_rooty() + self.widget.winfo_height() + 6
            self.tipwindow = tw = tk.Toplevel(self.widget)
            tw.wm_overrideredirect(True)
            tw.wm_geometry(f"+{x}+{y}")
            frame = tk.Frame(tw, background=self.bg, borderwidth=0)
            label = tk.Label(frame, text=self.text, justify=tk.LEFT,
                             background=self.bg, foreground=self.fg,
                             relief=tk.FLAT, borderwidth=0, padx=8, pady=4)
            label.pack()
            frame.pack()

        def hide(self, event=None):
            if self.tipwindow:
                self.tipwindow.destroy()
                self.tipwindow = None

    def _ui_call(self, fn, *args, **kwargs):
        """Executa uma função no thread da UI com after, se necessário."""
        if threading.current_thread() is threading.main_thread():
            return fn(*args, **kwargs)
        self.root.after(0, lambda: fn(*args, **kwargs))
        return None
    
    def setup_styles(self):
        """Configurar estilos e temas da interface (novo visual)."""
        self.style = ttk.Style()
        try:
            self.style.theme_use('clam')
        except Exception:
            pass

        # Temas disponíveis
        self.themes = {
            'Midnight': {
                'bg': '#0f172a',          # fundo geral (slate-900)
                'surface': '#111827',     # cartões
                'surface_alt': '#1f2937',
                'primary': '#7c3aed',     # roxo
                'primary_hover': '#6d28d9',
                'accent': '#22d3ee',      # ciano
                'success': '#10b981',
                'warning': '#f59e0b',
                'danger': '#ef4444',
                'text': '#e5e7eb',
                'muted': '#9ca3af',
                'border': '#334155'
            },
            'Aurora': {
                'bg': '#0b1020',
                'surface': '#101529',
                'surface_alt': '#1a2245',
                'primary': '#16a34a',     # verde aurora
                'primary_hover': '#15803d',
                'accent': '#60a5fa',      # azul
                'success': '#22c55e',
                'warning': '#f59e0b',
                'danger': '#ef4444',
                'text': '#e2e8f0',
                'muted': '#94a3b8',
                'border': '#27324d'
            },
            'Light': {
                'bg': '#f8fafc',
                'surface': '#ffffff',
                'surface_alt': '#f1f5f9',
                'primary': '#2563eb',
                'primary_hover': '#1d4ed8',
                'accent': '#7c3aed',
                'success': '#16a34a',
                'warning': '#d97706',
                'danger': '#dc2626',
                'text': '#0f172a',
                'muted': '#475569',
                'border': '#cbd5e1'
            }
        }

        # Tema ativo
        self.active_theme = 'Aurora'
        self.colors = self.themes[self.active_theme].copy()

        # Aplicar estilos iniciais
        self._configure_styles()

    def _configure_styles(self):
        """Aplicar estilos do tema atual."""
        try:
            self.root.configure(bg=self.colors['bg'])
        except Exception:
            pass

        base_font = ('Segoe UI', 10)
        title_font = ('Segoe UI Semibold', 14)
        header_font = ('Segoe UI Semibold', 16)

        # Frames
        self.style.configure('TFrame', background=self.colors['bg'])
        self.style.configure('App.TFrame', background=self.colors['bg'])
        self.style.configure('Header.TFrame', background=self.colors['surface_alt'])
        self.style.configure('Card.TFrame', background=self.colors['surface'])
        self.style.configure('TLabelframe', background=self.colors['surface'], foreground=self.colors['text'])
        self.style.configure('TLabelframe.Label', background=self.colors['surface'], foreground=self.colors['text'], font=title_font)

        # Labels
        self.style.configure('TLabel', background=self.colors['bg'], foreground=self.colors['text'], font=base_font)
        # Título genérico (fundo do app/bg)
        self.style.configure('Title.TLabel', background=self.colors['bg'], foreground=self.colors['text'], font=header_font)
        # Título para cabeçalho (fundo surface_alt)
        self.style.configure('Header.TLabel', background=self.colors['surface_alt'], foreground=self.colors['text'], font=header_font)
        # Informações (ajustado para superfícies por padrão)
        self.style.configure('Info.TLabel', background=self.colors['surface'], foreground=self.colors['muted'], font=base_font)
        # Labels para áreas com fundo de cartão/surface
        self.style.configure('Surface.TLabel', background=self.colors['surface'], foreground=self.colors['text'], font=base_font)
        self.style.configure('SurfaceInfo.TLabel', background=self.colors['surface'], foreground=self.colors['muted'], font=base_font)
        # Chips e câmera
        self.style.configure('Success.TLabel', background=self.colors['bg'], foreground=self.colors['success'], font=('Segoe UI', 10, 'bold'))
        self.style.configure('Warning.TLabel', background=self.colors['bg'], foreground=self.colors['warning'], font=('Segoe UI', 10, 'bold'))
        self.style.configure('Chip.TLabel', background=self.colors['surface_alt'], foreground=self.colors['text'], font=('Segoe UI', 9, 'bold'))
        self.style.configure('Camera.TLabel', background=self.colors['surface'], foreground=self.colors['text'], font=('Segoe UI Semibold', 14))

        # Botões
        def _btn(style_name, bg, hover, fg='#ffffff'):
            self.style.configure(style_name, background=bg, foreground=fg, font=('Segoe UI Semibold', 10), padding=(10, 6), borderwidth=0)
            self.style.map(style_name, background=[('active', hover)], foreground=[('disabled', self.colors['muted'])])
        _btn('Primary.TButton', self.colors['primary'], self.colors['primary_hover'])
        _btn('Accent.TButton', self.colors['accent'], self.colors['primary'])
        _btn('Success.TButton', self.colors['success'], '#0ea37f')
        _btn('Danger.TButton', self.colors['danger'], '#dc2626')
        _btn('Secondary.TButton', self.colors['surface_alt'], '#31475b', fg=self.colors['text'])

        # Notebook (abas)
        self.style.configure('TNotebook', background=self.colors['bg'], borderwidth=0)
        self.style.configure('TNotebook.Tab', background=self.colors['surface'], foreground=self.colors['muted'], padding=(12, 8))
        self.style.map('TNotebook.Tab', background=[('selected', self.colors['accent'])], foreground=[('selected', '#ffffff')])

        # Treeview (tabelas)
        self.style.configure('Treeview', background=self.colors['surface'], fieldbackground=self.colors['surface'], foreground=self.colors['text'], bordercolor=self.colors['border'], borderwidth=0, font=('Segoe UI', 10), rowheight=24)
        self.style.configure('Treeview.Heading', background=self.colors['surface_alt'], foreground=self.colors['text'], font=('Segoe UI Semibold', 10))
        self.style.map('Treeview.Heading', background=[('active', self.colors['surface_alt'])])

        # Panedwindow
        try:
            self.style.configure('TPanedwindow', background=self.colors['bg'])
        except Exception:
            pass

    def apply_theme(self, name: str):
        """Trocar tema em tempo real."""
        try:
            if name not in self.themes:
                return
            self.active_theme = name
            self.colors = self.themes[name].copy()
            self._configure_styles()
            # Atualizar chips de status se existirem
            if hasattr(self, 'webcam_chip'):
                self.webcam_chip.config(style='Chip.TLabel')
            if hasattr(self, 'c100_chip'):
                self.c100_chip.config(style='Chip.TLabel')
            # Forçar atualização de fundo da raiz
            self.root.configure(bg=self.colors['bg'])
            # Ajustar título do cabeçalho para o fundo do cabeçalho
            if hasattr(self, 'header_title'):
                try:
                    self.header_title.configure(style='Header.TLabel')
                except Exception:
                    pass
            # Reaplicar paleta na lista de notificações (widget Tk clássico)
            if hasattr(self, 'notifications_listbox'):
                try:
                    self.notifications_listbox.config(
                        bg=self.colors['surface'],
                        fg=self.colors['text'],
                        highlightbackground=self.colors['border'],
                        selectbackground=self.colors['accent'],
                        selectforeground='#ffffff'
                    )
                except Exception:
                    pass
            # Atualizar cores dos status das câmeras conforme tema atual
            try:
                if hasattr(self, 'camera_states'):
                    if 'Webcam' in self.camera_states:
                        self._update_camera_controls('Webcam')
                    if 'C100' in self.camera_states:
                        self._update_camera_controls('C100')
            except Exception:
                pass
        except Exception:
            pass
    
    def create_widgets(self):
        """Criar widgets da interface"""
        # Cabeçalho
        self.header_frame = ttk.Frame(self.root, padding=(10, 10), style='Header.TFrame')
        self.header_title = ttk.Label(self.header_frame, text="👁️  Escola Segura — Reconhecimento Facial", style='Title.TLabel')
        self.header_actions = ttk.Frame(self.header_frame)
        self.quick_start_btn = ttk.Button(self.header_actions, text="▶ Iniciar", style='Primary.TButton', command=self.start_all_cameras)
        self.quick_stop_btn = ttk.Button(self.header_actions, text="⏹ Parar", style='Danger.TButton', command=self.stop_all_cameras, state='disabled')
        self.quick_capture_btn = ttk.Button(self.header_actions, text="📸 Capturar", style='Secondary.TButton', command=self.capture_frame)
        self.header_title.pack(side='left')
        self.quick_start_btn.pack(side='left', padx=5)
        self.quick_stop_btn.pack(side='left', padx=5)
        self.quick_capture_btn.pack(side='left', padx=5)
        self.header_actions.pack(side='right')
        # Tooltips
        ModernFaceRecognitionGUI.Tooltip(self.quick_start_btn, 'Iniciar todas as câmeras')
        ModernFaceRecognitionGUI.Tooltip(self.quick_stop_btn, 'Parar todas as câmeras')
        ModernFaceRecognitionGUI.Tooltip(self.quick_capture_btn, 'Capturar frame atual')

        # Frame principal
        self.main_frame = ttk.Frame(self.root)

        # Frame superior - Câmera e controles
        self.camera_frame = ttk.LabelFrame(self.main_frame, text="Câmera ao Vivo", padding=10, style='TLabelframe')
        self.camera_label = ttk.Label(self.camera_frame, text="👋 Bem-vindo!\nClique em 'Iniciar' para começar a câmera.", style='Camera.TLabel', anchor='center')
        self.camera_label.pack(expand=True, fill='both')
        
        # Frame de controles
        self.controls_frame = ttk.Frame(self.camera_frame, style='Card.TFrame')
        
        # Controles gerais
        general_control_frame = ttk.LabelFrame(self.controls_frame, text="Controles Gerais", style='TLabelframe')
        general_control_frame.pack(fill=tk.X, pady=5)
        
        self.start_all_btn = ttk.Button(general_control_frame, text="▶ Iniciar Todas", 
                                       command=self.start_all_cameras, style='Primary.TButton')
        self.stop_all_btn = ttk.Button(general_control_frame, text="⏹ Parar Todas", 
                                      command=self.stop_all_cameras, state='disabled', style='Danger.TButton')
        self.capture_btn = ttk.Button(general_control_frame, text="📸 Capturar", 
                                     command=self.capture_frame, style='Secondary.TButton')
        
        self.start_all_btn.pack(side='left', padx=5)
        self.stop_all_btn.pack(side='left', padx=5)
        self.capture_btn.pack(side='left', padx=5)
        ModernFaceRecognitionGUI.Tooltip(self.start_all_btn, 'Iniciar todas as câmeras')
        ModernFaceRecognitionGUI.Tooltip(self.stop_all_btn, 'Parar todas as câmeras')
        ModernFaceRecognitionGUI.Tooltip(self.capture_btn, 'Salvar imagem atual')
        
        # Controles individuais das câmeras
        camera_control_frame = ttk.LabelFrame(self.controls_frame, text="Controles Individuais", style='TLabelframe')
        camera_control_frame.pack(fill=tk.X, pady=5)
        
        # Webcam
        webcam_frame = ttk.Frame(camera_control_frame, style='Card.TFrame')
        webcam_frame.pack(fill=tk.X, pady=2)
        ttk.Label(webcam_frame, text="Webcam:", style='Surface.TLabel').pack(side=tk.LEFT, padx=5)
        self.webcam_start_btn = ttk.Button(webcam_frame, text="▶", 
                                          command=lambda: self.start_camera('Webcam'))
        self.webcam_stop_btn = ttk.Button(webcam_frame, text="⏹", 
                                         command=lambda: self.stop_camera('Webcam'), state='disabled')
        self.webcam_status_label = ttk.Label(webcam_frame, text="Parada", style='Surface.TLabel', foreground=self.colors['danger'])
        
        self.webcam_start_btn.pack(side=tk.LEFT, padx=2)
        self.webcam_stop_btn.pack(side=tk.LEFT, padx=2)
        self.webcam_status_label.pack(side=tk.LEFT, padx=10)
        
        # C100
        c100_frame = ttk.Frame(camera_control_frame, style='Card.TFrame')
        c100_frame.pack(fill=tk.X, pady=2)
        ttk.Label(c100_frame, text="C100:", style='Surface.TLabel').pack(side=tk.LEFT, padx=5)
        self.c100_start_btn = ttk.Button(c100_frame, text="▶", 
                                        command=lambda: self.start_camera('C100'))
        self.c100_stop_btn = ttk.Button(c100_frame, text="⏹", 
                                       command=lambda: self.stop_camera('C100'), state='disabled')
        self.c100_status_label = ttk.Label(c100_frame, text="Parada", style='Surface.TLabel', foreground=self.colors['danger'])
        
        self.c100_start_btn.pack(side=tk.LEFT, padx=2)
        self.c100_stop_btn.pack(side=tk.LEFT, padx=2)
        self.c100_status_label.pack(side=tk.LEFT, padx=10)
        
        self.controls_frame.pack(side='bottom', fill='x', pady=10)
        
        # Frame lateral - Informações e controles
        self.info_frame = ttk.LabelFrame(self.main_frame, text="Informações do Sistema", padding=10, style='TLabelframe')
        
        # Abas para diferentes seções
        self.notebook = ttk.Notebook(self.info_frame)
        
        # Aba de reconhecimento
        self.recognition_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.recognition_tab, text="Reconhecimento")

        # Filtro de busca
        filter_bar = ttk.Frame(self.recognition_tab)
        ttk.Label(filter_bar, text="Filtro:").pack(side='left', padx=(0, 6))
        self.recognition_filter_var = tk.StringVar()
        filter_entry = ttk.Entry(filter_bar, textvariable=self.recognition_filter_var, width=28)
        filter_entry.pack(side='left', fill='x', expand=False)
        ttk.Button(filter_bar, text="Limpar", style='Secondary.TButton', command=lambda: (self.recognition_filter_var.set(''), self.refresh_recognition_table())).pack(side='left', padx=6)
        filter_entry.bind('<KeyRelease>', lambda e: self.refresh_recognition_table())
        filter_bar.pack(fill='x', pady=(0, 6))

        # Tabela de pessoas reconhecidas
        self.recognition_tree = ttk.Treeview(self.recognition_tab,
                                             columns=('Hora', 'Pessoa', 'Confiança', 'Emoção', 'Idade', 'Gênero'),
                                             show='headings', style='Treeview', height=10)
        for col, title, w in (
            ('Hora', 'Hora', 90),
            ('Pessoa', 'Pessoa', 140),
            ('Confiança', 'Confiança', 90),
            ('Emoção', 'Emoção', 120),
            ('Idade', 'Idade', 70),
            ('Gênero', 'Gênero', 90)
        ):
            self.recognition_tree.heading(col, text=title)
            self.recognition_tree.column(col, width=w, anchor='center')
        recognition_scrollbar = ttk.Scrollbar(self.recognition_tab, orient='vertical', command=self.recognition_tree.yview)
        self.recognition_tree.config(yscrollcommand=recognition_scrollbar.set)
        self.recognition_tree.pack(side='left', fill='both', expand=True)
        recognition_scrollbar.pack(side='right', fill='y')
        
        # Aba de presença
        self.attendance_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.attendance_tab, text="Presença")
        
        # Lista de presenças (eventos recentes)
        self.attendance_tree = ttk.Treeview(self.attendance_tab, columns=('Hora', 'Pessoa', 'Confiança'), show='headings', style='Treeview')
        self.attendance_tree.heading('Hora', text='Hora')
        self.attendance_tree.heading('Pessoa', text='Pessoa')
        self.attendance_tree.heading('Confiança', text='Confiança')

        self.attendance_scrollbar = ttk.Scrollbar(self.attendance_tab, orient='vertical')
        self.attendance_tree.config(yscrollcommand=self.attendance_scrollbar.set)
        self.attendance_scrollbar.config(command=self.attendance_tree.yview)

        # Resumo por pessoa
        summary_container = ttk.Frame(self.attendance_tab)
        summary_label = ttk.Label(summary_container, text='Resumo por Pessoa', style='Title.TLabel')
        summary_label.pack(anchor='w', pady=(5, 2))
        self.attendance_summary_tree = ttk.Treeview(summary_container,
                                                    columns=('Pessoa', 'Última Vista', 'Média Conf.', 'Registros Hoje'),
                                                    show='headings', height=8)
        for col, title in (
            ('Pessoa', 'Pessoa'),
            ('Última Vista', 'Última Vista'),
            ('Média Conf.', 'Média Conf.'),
            ('Registros Hoje', 'Registros Hoje')
        ):
            self.attendance_summary_tree.heading(col, text=title)
            self.attendance_summary_tree.column(col, width=120, anchor='center')
        summary_scrollbar = ttk.Scrollbar(summary_container, orient='vertical', command=self.attendance_summary_tree.yview)
        self.attendance_summary_tree.config(yscrollcommand=summary_scrollbar.set)
        self.attendance_summary_tree.pack(side='left', fill='both', expand=True)
        summary_scrollbar.pack(side='right', fill='y')

        # Layout das listas na aba
        left_container = ttk.Frame(self.attendance_tab)
        left_label = ttk.Label(left_container, text='Eventos Recentes', style='Title.TLabel')
        left_label.pack(anchor='w', pady=(5, 2))
        self.attendance_tree.pack(in_=left_container, side='left', fill='both', expand=True)
        self.attendance_scrollbar.pack(in_=left_container, side='right', fill='y')

        left_container.pack(side='left', fill='both', expand=True, padx=(0, 5))
        summary_container.pack(side='left', fill='both', expand=True, padx=(5, 0))
        
        # Aba de notificações
        self.notifications_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.notifications_tab, text="Notificações")
        
        # Lista de notificações
        self.notifications_listbox = tk.Listbox(self.notifications_tab, height=8, bg=self.colors['surface'], fg=self.colors['text'], highlightbackground=self.colors['border'], selectbackground=self.colors['accent'], selectforeground='#ffffff', relief='flat')
        self.notifications_scrollbar = ttk.Scrollbar(self.notifications_tab, orient='vertical')
        self.notifications_listbox.config(yscrollcommand=self.notifications_scrollbar.set)
        self.notifications_scrollbar.config(command=self.notifications_listbox.yview)
        
        self.notifications_listbox.pack(side='left', fill='both', expand=True)
        self.notifications_scrollbar.pack(side='right', fill='y')
        
        # Aba de configurações
        self.settings_tab = ttk.Frame(self.notebook, style='TFrame')
        self.notebook.add(self.settings_tab, text="Configurações")
        
        # Configurações do modelo
        ttk.Label(self.settings_tab, text="Modelo:").grid(row=0, column=0, sticky='w', padx=5, pady=5)
        # Usar configuração atual como padrão
        self.model_var = tk.StringVar(value=self.config.model.name)
        self.model_combo = ttk.Combobox(self.settings_tab, textvariable=self.model_var,
                        values=['Facenet', 'VGG-Face', 'OpenFace', 'DeepFace'])
        self.model_combo.grid(row=0, column=1, sticky='ew', padx=5, pady=5)

        # Threshold
        ttk.Label(self.settings_tab, text="Threshold:").grid(row=1, column=0, sticky='w', padx=5, pady=5)
        self.threshold_var = tk.DoubleVar(value=self.config.model.threshold)
        self.threshold_scale = ttk.Scale(self.settings_tab, from_=0.1, to=1.0,
                        variable=self.threshold_var, orient='horizontal')
        self.threshold_scale.grid(row=1, column=1, sticky='ew', padx=5, pady=5)

        # Detector backend
        ttk.Label(self.settings_tab, text="Detector:").grid(row=2, column=0, sticky='w', padx=5, pady=5)
        self.detector_var = tk.StringVar(value=self.config.model.detector_backend)
        self.detector_combo = ttk.Combobox(self.settings_tab, textvariable=self.detector_var,
                          values=['opencv', 'mtcnn', 'retinaface'])
        self.detector_combo.grid(row=2, column=1, sticky='ew', padx=5, pady=5)

        # Modo Performance na aba
        self.perf_chk = ttk.Checkbutton(self.settings_tab, text='Modo Performance (rápido, menor custo)', 
                                   variable=self.performance_mode_var, command=self.toggle_performance_mode)
        self.perf_chk.grid(row=3, column=0, columnspan=2, sticky='w', padx=5, pady=5)

        # Botão aplicar configurações
        ttk.Button(self.settings_tab, text="Aplicar Configurações",
               command=self.apply_settings).grid(row=4, column=0, columnspan=2, pady=10)

        # Configurar grid
        self.settings_tab.columnconfigure(1, weight=1)

        self.notebook.pack(fill='both', expand=True)
        # Forçar foco na primeira aba ao iniciar
        self.notebook.select(0)
        # Frame inferior - Estatísticas
        self.stats_frame = ttk.LabelFrame(self.main_frame, text="Estatísticas em Tempo Real", padding=10, style='TLabelframe')
        # Labels de estatísticas
        self.fps_label = ttk.Label(self.stats_frame, text="FPS: 0.0", style='Info.TLabel')
        self.memory_label = ttk.Label(self.stats_frame, text="Memória: 0 MB", style='Info.TLabel')
        self.recognitions_label = ttk.Label(self.stats_frame, text="Reconhecimentos: 0", style='Info.TLabel')
        self.attendance_label = ttk.Label(self.stats_frame, text="Presenças hoje: 0", style='Info.TLabel')
        self.fps_label.pack(side='left', padx=12)
        self.memory_label.pack(side='left', padx=12)
        self.recognitions_label.pack(side='left', padx=12)
        self.attendance_label.pack(side='left', padx=12)

        # Barra de status inferior (indicadores das câmeras)
        self.status_bar = ttk.Frame(self.root, padding=(10, 6), style='App.TFrame')
        self.webcam_chip = ttk.Label(self.status_bar, text='● Webcam: Parada', foreground=self.colors['danger'], style='Chip.TLabel')
        self.c100_chip = ttk.Label(self.status_bar, text='● C100: Parada', foreground=self.colors['danger'], style='Chip.TLabel')
        self.webcam_chip.pack(side='left', padx=(0, 15))
        self.c100_chip.pack(side='left')
        # Menu bar
        self.create_menu()

        # Atalhos de teclado
        self.root.bind('<Control-r>', lambda e: self.start_all_cameras())
        self.root.bind('<Control-s>', lambda e: self.stop_all_cameras())
        self.root.bind('<F11>', lambda e: self.toggle_fullscreen())
        self.root.bind('<Escape>', lambda e: self.exit_fullscreen())
        # Atalhos de zoom (Ctrl+= / Ctrl+- / Ctrl+0)
        self.root.bind('<Control-equal>', lambda e: self.change_ui_scale(0.1))
        self.root.bind('<Control-plus>', lambda e: self.change_ui_scale(0.1))
        self.root.bind('<Control-KP_Add>', lambda e: self.change_ui_scale(0.1))
        self.root.bind('<Control-minus>', lambda e: self.change_ui_scale(-0.1))
        self.root.bind('<Control-KP_Subtract>', lambda e: self.change_ui_scale(-0.1))
        self.root.bind('<Control-0>', lambda e: self.reset_ui_scale())
        # Atalhos extras de emergência
        self.root.bind('<F12>', lambda e: self.reset_layout())
        self.root.bind('<Control-Alt-Key-1>', lambda e: self.set_ui_scale(1.25))
        self.root.bind('<Control-Alt-Key-2>', lambda e: self.set_ui_scale(1.5))
        self.root.bind('<Control-Alt-Key-3>', lambda e: self.set_ui_scale(2.0))
    
    def create_menu(self):
        """Criar barra de menu"""
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)
        
        # Menu Arquivo
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Arquivo", menu=file_menu)
        file_menu.add_command(label="Adicionar Pessoa...", command=self.add_person)
        file_menu.add_command(label="Gerenciar Pessoas...", command=self.manage_people)
        file_menu.add_separator()
        file_menu.add_command(label="Recriar Embeddings...", command=self.rebuild_embeddings)
        file_menu.add_separator()
        file_menu.add_command(label="Reiniciar Câmeras", command=self.restart_cameras)
        file_menu.add_separator()
        file_menu.add_command(label="Configurar Câmeras...", command=self.open_camera_config_dialog)
        file_menu.add_separator()
        file_menu.add_command(label="Exportar Dados...", command=self.export_data)
        file_menu.add_command(label="Importar Dados...", command=self.import_data)
        file_menu.add_separator()
        file_menu.add_command(label="Sair", command=self.root.quit)
        
        # Menu Visualização
        view_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Visualização", menu=view_menu)
        view_menu.add_command(label="Mostrar/Ocultar FPS", command=self.toggle_fps)
        view_menu.add_command(label="Mostrar/Ocultar Confiança", command=self.toggle_confidence)
        view_menu.add_command(label="Mostrar/Ocultar Emoções", command=self.toggle_emotions)
        view_menu.add_command(label="Mostrar/Ocultar Idade/Gênero", command=self.toggle_age_gender)
        view_menu.add_checkbutton(label="Modo Performance", onvalue=True, offvalue=False, 
                                  variable=self.performance_mode_var, command=self.toggle_performance_mode)
        view_menu.add_separator()
        view_menu.add_command(label="Tela Cheia (F11)", command=self.toggle_fullscreen)
        zoom_menu = tk.Menu(view_menu, tearoff=0)
        zoom_menu.add_command(label="Redefinir (125%)", command=lambda: self.set_ui_scale(1.25))
        zoom_menu.add_separator()
        zoom_menu.add_command(label="100%", command=lambda: self.set_ui_scale(1.0))
        zoom_menu.add_command(label="125%", command=lambda: self.set_ui_scale(1.25))
        zoom_menu.add_command(label="150%", command=lambda: self.set_ui_scale(1.5))
        zoom_menu.add_command(label="175%", command=lambda: self.set_ui_scale(1.75))
        zoom_menu.add_command(label="200%", command=lambda: self.set_ui_scale(2.0))
        view_menu.add_cascade(label="Zoom", menu=zoom_menu)
        # Largura do painel lateral (ajusta proporção do divisor)
        ratio_menu = tk.Menu(view_menu, tearoff=0)
        ratio_options = [
            ("50% / 50%", 0.50),
            ("60% / 40%", 0.60),
            ("65% / 35%", 0.65),
            ("70% / 30%", 0.70),
            ("75% / 25%", 0.75),
            ("80% / 20%", 0.80),
        ]
        for label, r in ratio_options:
            ratio_menu.add_command(label=label, command=lambda v=r: self.set_side_panel_ratio(v))
        view_menu.add_cascade(label="Largura do Painel", menu=ratio_menu)
        # Tema (Midnight / Aurora / Light)
        theme_menu = tk.Menu(view_menu, tearoff=0)
        self.theme_var = getattr(self, 'theme_var', tk.StringVar(value=self.active_theme))
        for t in ('Aurora', 'Midnight', 'Light'):
            theme_menu.add_radiobutton(label=t, value=t, variable=self.theme_var,
                                       command=lambda tv=self.theme_var: self.apply_theme(tv.get()))
        view_menu.add_cascade(label="Tema", menu=theme_menu)
        view_menu.add_separator()
        view_menu.add_command(label="Redefinir Layout", command=self.reset_layout)
        
        # Menu Relatórios
        reports_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Relatórios", menu=reports_menu)
        reports_menu.add_command(label="Relatório de Presença", command=self.attendance_report)
        reports_menu.add_command(label="Relatório de Analytics", command=self.analytics_report)
        reports_menu.add_command(label="Exportar Analytics", command=self.export_analytics)
        
        # Menu Ajuda
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="Ajuda", menu=help_menu)
        help_menu.add_command(label="Sobre", command=self.show_about)
        help_menu.add_command(label="Documentação", command=self.show_documentation)
    
    def setup_layout(self):
        """Configurar layout da interface"""
        self.header_frame.pack(fill='x')
        self.main_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Divisor redimensionável entre vídeo e informações
        self.paned = ttk.Panedwindow(self.main_frame, orient=tk.HORIZONTAL)
        self.paned.pack(fill='both', expand=True)
        self.paned.add(self.camera_frame, weight=3)
        self.paned.add(self.info_frame, weight=1)
        # Re-embutir frames em containers dedicados para evitar problemas de parent/geometry
        try:
            # Remover panes antigos e recriar com containers
            self.paned.forget(self.camera_frame)
            self.paned.forget(self.info_frame)
            self.camera_container = ttk.Frame(self.paned, style='App.TFrame')
            self.info_container = ttk.Frame(self.paned, style='App.TFrame')
            self.paned.add(self.camera_container, weight=3)
            self.paned.add(self.info_container, weight=1)
            # Empacotar frames existentes dentro dos containers
            try:
                self.camera_frame.pack(in_=self.camera_container, fill='both', expand=True)
            except Exception:
                pass
            try:
                self.info_frame.pack(in_=self.info_container, fill='both', expand=True)
            except Exception:
                pass
        except Exception:
            pass
        # Evitar que o painel lateral colapse completamente
        try:
            # Aumentar mínimos para evitar colapso visual
            self.paned.paneconfigure(getattr(self, 'camera_container', self.camera_frame), minsize=640)
            self.paned.paneconfigure(getattr(self, 'info_container', self.info_frame), minsize=360)
        except Exception:
            pass

        # Definir posição inicial confortável do divisor após renderização
        def _set_initial_sash():
            try:
                total = self.paned.winfo_width() or self.root.winfo_width() or 1400
                # 72% para vídeo, 28% para painel lateral
                self.paned.sashpos(0, int(total * 0.72))
            except Exception:
                pass
        self.root.after(150, _set_initial_sash)
        self.root.after(600, _set_initial_sash)
        # Bind para manter proporção ao redimensionar
        try:
            self._sash_ratio = 0.72
            # Permitir ajuste via variável de ambiente (0.55 a 0.9)
            try:
                env_ratio = os.getenv('SIDE_PANEL_RATIO') or os.getenv('UI_SIDE_RATIO')
                if env_ratio:
                    r = float(env_ratio)
                    if r < 0.55:
                        r = 0.55
                    if r > 0.9:
                        r = 0.9
                    self._sash_ratio = r
            except Exception:
                pass
            self._setting_sash = False
            self.paned.bind('<Configure>', self._on_paned_configure)
        except Exception:
            pass

        # Estatísticas ocupam toda a largura
        self.stats_frame.pack(in_=self.main_frame, fill='x', pady=(6, 0))
        # Status bar no fim
        self.status_bar.pack(fill='x', side='bottom')

    def set_side_panel_ratio(self, ratio: float):
        """Definir proporção do painel de vídeo/lateral e reposicionar sash."""
        try:
            # Limites seguros
            r = float(ratio)
            if r < 0.55:
                r = 0.55
            if r > 0.90:
                r = 0.90
            self._sash_ratio = r
            # Aplicar imediatamente
            if hasattr(self, '_set_sash_by_ratio'):
                self._set_sash_by_ratio()
        except Exception:
            pass

    def _post_init_layout_fix(self):
        """Ajustar sash do Panedwindow quando a janela já tem dimensões reais."""
        try:
            self.root.update_idletasks()
            total = int(self.paned.winfo_width())
            if not total or total < 600:
                total = int(self.root.winfo_width())
            if not total or total < 600:
                # Tentar novamente um pouco depois, até estabilizar
                self.root.after(200, self._post_init_layout_fix)
                return
            self.paned.sashpos(0, int(total * 0.72))
            # Garantir placeholder visível no início
            if hasattr(self, 'show_camera_placeholder') and not self.is_running:
                self.show_camera_placeholder(True)
        except Exception:
            pass

    def _on_paned_configure(self, event=None):
        """Manter proporção do sash ao redimensionar, respeitando mínimos."""
        try:
            if getattr(self, '_setting_sash', False):
                return
            self._set_sash_by_ratio()
        except Exception:
            pass

    def _set_sash_by_ratio(self, ratio: float = None):
        """Aplicar sash baseado em proporção e mínimos (evita painel minúsculo)."""
        try:
            self.root.update_idletasks()
            total = int(self.paned.winfo_width())
            if not total or total < 200:
                total = int(self.root.winfo_width())
            if not total or total < 200:
                # tentar mais tarde
                self.root.after(200, self._post_init_layout_fix)
                return
            r = ratio if ratio is not None else getattr(self, '_sash_ratio', 0.72)
            min_cam = 640
            min_info = 360
            pos = int(total * r)
            # clamp por mínimos
            if total - pos < min_info:
                pos = total - min_info
            if pos < min_cam:
                pos = min_cam
            if pos < 0:
                pos = int(total * 0.7)
            self._setting_sash = True
            try:
                self.paned.sashpos(0, pos)
            finally:
                self._setting_sash = False
        except Exception:
            pass

    def toggle_performance_mode(self):
        """Ativar/Desativar Modo Performance: usa detector OpenCV e aumenta frame_skip."""
        enabled = bool(self.performance_mode_var.get())
        try:
            if enabled:
                # Salvar atuais e aplicar modo performance
                self._saved_backend = self.config.model.detector_backend
                self._saved_frame_skip = self.config.camera.frame_skip
                self.config.model.detector_backend = 'opencv'
                self.config.camera.frame_skip = max(3, self._saved_frame_skip)
                self.notification_system.add_notification('info', 'Modo Performance: ativado (OpenCV + frame_skip alto)')
            else:
                # Restaurar
                self.config.model.detector_backend = getattr(self, '_saved_backend', self.config.model.detector_backend)
                self.config.camera.frame_skip = getattr(self, '_saved_frame_skip', self.config.camera.frame_skip)
                self.notification_system.add_notification('info', 'Modo Performance: desativado')
            self.update_notifications()

            # Reaplicar configuração nos sistemas
            if self.system:
                try:
                    self.system.stop_processing_thread()
                except Exception:
                    pass
                self.system = FaceRecognitionSystemV2(self.config)
            # Reiniciar multi-câmeras se estiverem ativas
            was_running = self.is_running
            if self.multi_system:
                try:
                    self.multi_system.stop()
                except Exception:
                    pass
                self.multi_system = None
            if was_running:
                self.start_multi_camera()
        except Exception as e:
            logger.error(f"Erro ao alternar Modo Performance: {e}")

    def rebuild_embeddings(self):
        """Recriar embeddings a partir da pasta known_faces."""
        try:
            if not messagebox.askyesno('Recriar Embeddings', 'Deseja recriar os embeddings a partir de known_faces? Isso pode levar alguns minutos.'):
                return
            # Parar processamento para evitar conflitos de I/O
            if self.system:
                try:
                    self.system.stop_processing_thread()
                except Exception:
                    pass
            if self.multi_system:
                try:
                    self.multi_system.stop()
                except Exception:
                    pass
            # Recriar via ambos, se disponíveis
            ok_any = False
            try:
                if self.system:
                    ok_any = self.system.face_recognizer.create_embeddings() or ok_any
            except Exception:
                pass
            try:
                if self.multi_system:
                    ok_any = self.multi_system.face_recognizer.create_embeddings() or ok_any
            except Exception:
                pass
            if not ok_any:
                # Fallback: instância temporária
                from face_recognizer import FaceRecognizer
                fr = FaceRecognizer(known_faces_dir=self.config.known_faces_dir, embeddings_file=self.config.embeddings_file, model_name=self.config.model.name, threshold=self.config.model.threshold)
                ok_any = fr.create_embeddings()
            if ok_any:
                messagebox.showinfo('Embeddings', 'Embeddings recriados com sucesso.')
                self.notification_system.add_notification('info', 'Embeddings recriados com sucesso')
            else:
                messagebox.showwarning('Embeddings', 'Nenhuma imagem processada. Verifique a pasta known_faces/.')
                self.notification_system.add_notification('warning', 'Falha ao recriar embeddings')
            self.update_notifications()
            # Recarregar sistemas com novos embeddings
            self.initialize_system()
            if self.is_running:
                self.start_multi_camera()
        except Exception as e:
            logger.error(f"Erro ao recriar embeddings: {e}")
            messagebox.showerror('Erro', f'Falha ao recriar embeddings: {e}')
    def initialize_system(self):
        """Inicializar sistema de reconhecimento"""
        try:
            self.system = FaceRecognitionSystemV2(self.config)
            self.notification_system.add_notification('info', 'Sistema inicializado com sucesso')
            self.update_notifications()
            # Carregar configuração de câmeras
            self.load_camera_settings()
        except Exception as e:
            messagebox.showerror("Erro", f"Falha ao inicializar sistema: {str(e)}")
            logger.error(f"Erro na inicialização: {e}")

    def start_multi_camera(self):
        """Iniciar múltiplas câmeras (Webcam em cima, C100 abaixo)"""
        if self.multi_system is not None:
            messagebox.showwarning("Atenção", "Múltiplas câmeras já estão em execução")
            return
        try:
            self.multi_system = MultiCameraSystem(self.config)
            # Adicionar Webcam (local_0) e C100 (RTSP) conforme configuração
            webcam_enabled = self.camera_config.get('webcam_enabled', True)
            c100_enabled = self.camera_config.get('c100_enabled', False)
            c100_url = self.camera_config.get('c100_url') or os.getenv('RTSP_C100_URL', '')
            c100_added = False
            if webcam_enabled:
                self.multi_system.add_camera('Webcam', 'local', '0', 640, 480)
            if c100_enabled and c100_url:
                c100_added = self.multi_system.add_camera('C100', 'rtsp', c100_url, 640, 480)
            elif c100_enabled and not c100_url:
                logger.warning('C100 habilitada, mas RTSP_C100_URL não definida; ignorando C100')

            ok = self.multi_system.initialize_system()
            if not ok:
                raise RuntimeError('Falha ao inicializar multi-câmeras')
            self.multi_system.start_processing()

            # Marcar câmeras como ativas na UI ao iniciar todas
            # Atualizar estado de execução conforme câmeras adicionadas
            self.camera_states['Webcam']['running'] = bool(webcam_enabled)
            self._update_camera_controls('Webcam')
            if c100_added:
                self.camera_states['C100']['running'] = True
            else:
                self.camera_states['C100']['running'] = False
            self._update_camera_controls('C100')

            # Iniciar thread de exibição
            self.is_running = True
            self.start_all_btn.config(state='disabled')
            self.stop_all_btn.config(state='normal')
            # Sincronizar botões rápidos do cabeçalho
            if hasattr(self, 'quick_start_btn') and hasattr(self, 'quick_stop_btn'):
                self.quick_start_btn.config(state='disabled')
                self.quick_stop_btn.config(state='normal')
            self.multi_camera_thread = threading.Thread(target=self.multi_camera_loop, daemon=True)
            self.multi_camera_thread.start()
            # Ocultar placeholder assim que iniciar
            if hasattr(self, 'show_camera_placeholder'):
                self.show_camera_placeholder(False)
            self.notification_system.add_notification('info', 'Múltiplas câmeras iniciadas')
            self.update_notifications()
        except Exception as e:
            logger.error(f"Erro ao iniciar múltiplas câmeras: {e}")
            messagebox.showerror("Erro", f"Falha ao iniciar múltiplas câmeras: {str(e)}")

    def stop_multi_camera(self):
        """Parar múltiplas câmeras"""
        try:
            self.is_running = False
            if self.multi_system:
                self.multi_system.stop()
                self.multi_system = None
            # Atualizar estado das câmeras
            for cam in self.camera_states.keys():
                self.camera_states[cam]['running'] = False
            self._update_camera_controls('Webcam')
            self._update_camera_controls('C100')
            self.start_all_btn.config(state='normal')
            self.stop_all_btn.config(state='disabled')
            if hasattr(self, 'quick_start_btn') and hasattr(self, 'quick_stop_btn'):
                self.quick_start_btn.config(state='normal')
                self.quick_stop_btn.config(state='disabled')
            # Exibir placeholder de câmera parada
            if hasattr(self, 'show_camera_placeholder'):
                self.show_camera_placeholder(True)
            self.notification_system.add_notification('info', 'Múltiplas câmeras paradas')
            self.update_notifications()
        except Exception as e:
            logger.error(f"Erro ao parar múltiplas câmeras: {e}")
    
    def start_all_cameras(self):
        """Iniciar todas as câmeras"""
        self.start_multi_camera()
    
    def stop_all_cameras(self):
        """Parar todas as câmeras"""
        self.stop_multi_camera()
    
    def start_camera(self, camera_name: str):
        """Iniciar uma câmera específica"""
        if self.camera_states[camera_name]['running']:
            return
        
        try:
            if self.multi_system is None:
                self.multi_system = MultiCameraSystem(self.config)
                # Adicionar ambas as câmeras
                if self.camera_config.get('webcam_enabled', True):
                    self.multi_system.add_camera('Webcam', 'local', '0', 640, 480)
                c100_url = self.camera_config.get('c100_url') or os.getenv('RTSP_C100_URL', '')
                if self.camera_config.get('c100_enabled', False) and c100_url:
                    self.multi_system.add_camera('C100', 'rtsp', c100_url, 640, 480)
                self.multi_system.initialize_system()
                self.multi_system.start_processing()
            
            # Iniciar thread de exibição se não estiver rodando
            if not self.is_running:
                self.is_running = True
                self.multi_camera_thread = threading.Thread(target=self.multi_camera_loop, daemon=True)
                self.multi_camera_thread.start()
            
            # Impedir ativação da C100 sem URL configurada
            if camera_name == 'C100':
                c100_url = self.camera_config.get('c100_url') or os.getenv('RTSP_C100_URL', '')
                if not c100_url:
                    messagebox.showwarning("Atenção", "RTSP_C100_URL não configurada. Não é possível iniciar a C100.")
                    self.camera_states['C100']['running'] = False
                    self._update_camera_controls('C100')
                    return

            self.camera_states[camera_name]['running'] = True
            self._update_camera_controls(camera_name)
            self.notification_system.add_notification('info', f'{camera_name} iniciada')
            self.update_notifications()
            # Atualizar botões rápidos
            if hasattr(self, 'quick_start_btn') and hasattr(self, 'quick_stop_btn'):
                self.quick_start_btn.config(state='disabled')
                self.quick_stop_btn.config(state='normal')
            
        except Exception as e:
            logger.error(f"Erro ao iniciar {camera_name}: {e}")
            messagebox.showerror("Erro", f"Falha ao iniciar {camera_name}: {str(e)}")
    
    def stop_camera(self, camera_name: str):
        """Parar uma câmera específica"""
        if not self.camera_states[camera_name]['running']:
            return
        
        try:
            self.camera_states[camera_name]['running'] = False
            self._update_camera_controls(camera_name)
            self.notification_system.add_notification('info', f'{camera_name} parada')
            self.update_notifications()
            # Se nenhuma câmera estiver ativa, ajustar botões rápidos
            if hasattr(self, 'quick_start_btn') and hasattr(self, 'quick_stop_btn'):
                any_running = any(state['running'] for state in self.camera_states.values())
                self.quick_start_btn.config(state='disabled' if any_running else 'normal')
                self.quick_stop_btn.config(state='normal' if any_running else 'disabled')
            
        except Exception as e:
            logger.error(f"Erro ao parar {camera_name}: {e}")
    
    def _update_camera_controls(self, camera_name: str):
        """Atualizar controles de uma câmera específica"""
        is_running = self.camera_states[camera_name]['running']

        if camera_name == 'Webcam':
            self.webcam_start_btn.config(state='disabled' if is_running else 'normal')
            self.webcam_stop_btn.config(state='normal' if is_running else 'disabled')
            self.webcam_status_label.config(text="Ativa" if is_running else "Parada", 
                                          foreground=self.colors['success'] if is_running else self.colors['danger'])
            # Atualizar chip de status inferior
            if hasattr(self, 'webcam_chip'):
                self.webcam_chip.config(text=f"{'●' if is_running else '●'} Webcam: {'Ativa' if is_running else 'Parada'}", 
                                        foreground=self.colors['success'] if is_running else self.colors['danger'])
        elif camera_name == 'C100':
            self.c100_start_btn.config(state='disabled' if is_running else 'normal')
            self.c100_stop_btn.config(state='normal' if is_running else 'disabled')
            self.c100_status_label.config(text="Ativa" if is_running else "Parada", 
                                        foreground=self.colors['success'] if is_running else self.colors['danger'])
            if hasattr(self, 'c100_chip'):
                self.c100_chip.config(text=f"{'●' if is_running else '●'} C100: {'Ativa' if is_running else 'Parada'}", 
                                      foreground=self.colors['success'] if is_running else self.colors['danger'])
    
    def start_recognition(self):
        """Iniciar reconhecimento facial"""
        # Trocar para modo multi-câmeras conforme solicitado
        self.start_multi_camera()
    
    def stop_recognition(self):
        """Parar reconhecimento facial"""
        self.stop_multi_camera()
    
    def camera_loop(self):
        """Loop principal da câmera"""
        # Tentar backend otimizado no Windows
        try:
            cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)
        except Exception:
            cap = cv2.VideoCapture(0)
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        try:
            cap.set(cv2.CAP_PROP_FPS, 30)
            # MJPG costuma melhorar FPS em webcams
            cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        except Exception:
            pass
        
        if not cap.isOpened():
            self.notification_system.add_notification('error', 'Erro ao abrir câmera')
            self.update_notifications()
            return
        
        # Iniciar processamento do sistema (se aplicável)
        try:
            self.system.start_processing_thread()
        except Exception:
            pass
        
        while self.is_running:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Flip horizontal
            frame = cv2.flip(frame, 1)
            
            # Processar frame com estride para manter vídeo fluido
            self._single_cam_counter += 1
            do_analysis = (self._single_cam_counter % max(1, int(self._single_cam_stride))) == 0
            if do_analysis:
                try:
                    results = self.system.process_frame(frame)
                    self.process_recognition_results(frame, results)
                except Exception:
                    pass
            
            # Atualizar interface
            self.update_camera_display(frame)
            self.update_statistics()
            
            # Evitar travar a UI; pequena folga
            time.sleep(0.01)
        
        cap.release()

    def multi_camera_loop(self):
        """Loop principal de múltiplas câmeras (empilhamento vertical)"""
        while self.is_running and self.multi_system is not None:
            try:
                # Coletar resultados apenas das câmeras ativas
                all_results = []
                camera_frames = {}  # Armazenar frames originais atualizados
                updated_cameras = set()
                for camera in self.multi_system.cameras:
                    camera_name = camera.source_id
                    if not self.camera_states[camera_name]['running']:
                        continue
                    q = self.multi_system.frame_queues[camera_name]
                    data = None
                    if not q.empty():
                        # Drenar a fila e ficar com o frame mais recente (baixa latência)
                        try:
                            while not q.empty():
                                data = q.get_nowait()
                        except Exception:
                            data = None
                    if data is not None:
                        frame = data['frame']
                        camera_frames[camera_name] = frame  # Armazenar frame original
                        # Decidir se roda análise neste frame (para manter vídeo fluido)
                        c = self._ui_cam_counter.get(camera_name, 0) + 1
                        self._ui_cam_counter[camera_name] = c
                        do_analysis = (c % max(1, int(self._ui_cam_stride))) == 0
                        if do_analysis:
                            # Rodar análise e atualizar resultado
                            result = self.multi_system.process_frame(camera_name, frame)
                            result['camera_name'] = camera_name
                            # Garantir que o frame de exibição seja o mais recente
                            result['frame'] = frame
                            all_results.append(result)
                            self._last_display[camera_name] = {'result': result, 'frame': frame}
                            updated_cameras.add(camera_name)
                        else:
                            # Reusar último resultado, mas com frame atual (sem reprocessar)
                            if camera_name in self._last_display:
                                last = self._last_display[camera_name]['result']
                                fast_result = dict(last)
                                fast_result['frame'] = frame
                                fast_result['camera_name'] = camera_name
                                all_results.append(fast_result)
                            else:
                                # Sem resultado anterior ainda — usar mínimo com frame atual
                                all_results.append({
                                    'camera_id': camera_name,
                                    'camera_name': camera_name,
                                    'faces': [],
                                    'frame': frame,
                                    'timestamp': time.time()
                                })
                    else:
                        # Sem novo frame: usar último resultado disponível para manter layout estável
                        if camera_name in self._last_display:
                            last = self._last_display[camera_name]
                            last_result = dict(last['result'])
                            # Atualizar timestamp e garantir frame presente
                            last_result['timestamp'] = time.time()
                            last_result['frame'] = last.get('frame', last_result.get('frame'))
                            last_result['camera_name'] = camera_name
                            all_results.append(last_result)
                        else:
                            # Placeholder preto até chegarem frames
                            placeholder = np.zeros((480, 640, 3), dtype=np.uint8)
                            placeholder_result = {
                                'camera_id': camera_name,
                                'camera_name': camera_name,
                                'faces': [],
                                'frame': placeholder,
                                'timestamp': time.time()
                            }
                            all_results.append(placeholder_result)

                if all_results:
                    # Processar resultados de reconhecimento para cada câmera
                    for result in all_results:
                        if 'faces' in result and result['faces']:
                            # Extrair informações de reconhecimento de cada face (incluindo desconhecidos)
                            recognition_results = []
                            for face in result['faces']:
                                name = face.get('name') or 'Desconhecido'
                                confidence = face.get('confidence', 0.0)
                                bbox = face.get('bbox')
                                if bbox is None:
                                    continue
                                # Passar emoção e idade/gênero para evitar recálculo
                                recognition_results.append({
                                    'name': name,
                                    'similarity': confidence,
                                    'bbox': bbox,
                                    'emotion': face.get('emotion'),
                                    'age_gender': {
                                        'age': face.get('age'),
                                        'gender': face.get('gender')
                                    }
                                })
                            # Processar reconhecimentos se houver
                            if recognition_results and (result.get('camera_name') in updated_cameras):
                                # Usar o frame original para processamento apenas se houve atualização
                                camera_name = result.get('camera_name') or result.get('camera_id')
                                if camera_name in updated_cameras and camera_name in camera_frames:
                                    original_frame = camera_frames[camera_name]
                                    self.process_recognition_results(original_frame, recognition_results)
                                    self.update_notifications()
                    
                    # Compor verticalmente com resolução maior, baseada no espaço disponível
                    try:
                        disp_w = max(900, self.camera_frame.winfo_width() - 20)
                        disp_h = max(620, self.camera_frame.winfo_height() - 180)
                    except Exception:
                        disp_w, disp_h = 1100, 700
                    display_frame = self.multi_system.create_display_frame_vertical(
                        all_results, display_width=disp_w, display_height=disp_h
                    )
                    # Renderizar com double buffer
                    self._ui_call(self._render_frame, display_frame)
                else:
                    # Não limpar a imagem anterior para evitar flicker; apenas seguir
                    pass

                self.update_statistics()
                # Pequena folga para UI processar eventos sem travar a renderização
                time.sleep(0.01)
            except Exception as e:
                logger.error(f"Erro no loop de múltiplas câmeras: {e}")
                time.sleep(0.2)
    
    def process_recognition_results(self, frame: np.ndarray, results: List[Dict[str, Any]]):
        """Processar resultados de reconhecimento"""
        for result in results:
            person_name = result['name']
            similarity = result['similarity']
            bbox = result['bbox']
            
            # Verificar cooldown
            current_time = time.time()
            if person_name in self.last_recognition_time:
                if current_time - self.last_recognition_time[person_name] < self.recognition_cooldown:
                    continue
            
            self.last_recognition_time[person_name] = current_time
            
            # Detectar emoção e idade/gênero
            x, y, w, h = bbox
            # Garantir que a região da face esteja dentro dos limites do frame
            fh, fw = frame.shape[:2]
            x0 = max(0, int(x))
            y0 = max(0, int(y))
            x1 = min(fw, int(x + w))
            y1 = min(fh, int(y + h))
            if x1 <= x0 or y1 <= y0:
                continue
            face_region = frame[y0:y1, x0:x1]

            # Preferir dados já calculados pelo sistema multi-câmeras
            emotion_data = result.get('emotion')
            age_gender_data = result.get('age_gender') or {}

            # Respeitar toggles para (não) calcular
            want_emotion = True
            want_age_gender = True
            if self.multi_system is not None:
                want_emotion = getattr(self.multi_system, 'enable_emotion', True)
                want_age_gender = getattr(self.multi_system, 'enable_age_gender', True)
            else:
                want_emotion = getattr(self, '_show_emotions', True)
                want_age_gender = getattr(self, '_show_age_gender', True)

            if emotion_data is None and want_emotion:
                emotion_data = self.emotion_detector.detect_emotion(face_region)
            if (not age_gender_data) and want_age_gender:
                age_gender_data = self.age_gender_detector.detect_age_gender(face_region)
            if not emotion_data:
                emotion_data = {'emotion': 'neutral', 'confidence': 0.0}
            if not age_gender_data:
                age_gender_data = {'age': 0, 'gender': 'Unknown'}
            
            # Registrar presença
            if person_name != "Desconhecido":
                self.attendance_tracker.record_presence(person_name, similarity)
                self.analytics.record_recognition(person_name, similarity, 
                                                emotion_data['emotion'], 
                                                age_gender_data['age'], 
                                                age_gender_data['gender'])
            
            # Adicionar notificação
            if person_name != "Desconhecido":
                self.notification_system.add_notification(
                    'recognition', 
                    f"{person_name} reconhecido (confiança: {similarity:.2f})",
                    person_name
                )
            else:
                self.notification_system.add_notification(
                    'unknown', 
                    "Pessoa desconhecida detectada"
                )
            
            # Atualizar listas (respeitar toggles)
            # Atualizações de UI devem rodar no thread principal
            self._ui_call(self.update_recognition_list, person_name, similarity, emotion_data if want_emotion else {'emotion': 'neutral'}, age_gender_data if want_age_gender else {'age': 0, 'gender': 'Unknown'})
            self._ui_call(self.update_attendance_list)
            self._ui_call(self.update_attendance_summary)
            self._ui_call(self.update_notifications)
    
    def update_camera_display(self, frame: np.ndarray):
        """Atualizar exibição da câmera"""
        # Redimensionar frame para exibição — priorizar área maior para o vídeo
        height, width = frame.shape[:2]
        try:
            avail_w = max(900, self.camera_frame.winfo_width() - 20)
            # Reservar parte da altura para controles dentro do frame de câmera
            avail_h = max(600, self.camera_frame.winfo_height() - 180)
        except Exception:
            avail_w, avail_h = 1100, 700

        if width > avail_w or height > avail_h:
            scale = min(avail_w/width, avail_h/height)
            new_width = int(width * scale)
            new_height = int(height * scale)
            frame = cv2.resize(frame, (new_width, new_height))

        # Renderizar usando double buffer
        self._ui_call(self._render_frame, frame)

    def _render_frame(self, frame: np.ndarray):
        """Renderizar no Label usando double buffer para evitar flicker."""
        try:
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            image = Image.fromarray(frame_rgb)
            photo = ImageTk.PhotoImage(image)
            # Alternar entre buffers
            if self._photo_toggle:
                self._photo_front = photo
                active = self._photo_front
            else:
                self._photo_back = photo
                active = self._photo_back
            self._photo_toggle = not self._photo_toggle
            self.camera_label.config(image=active, text="")
            # Manter ambas referências vivas
            self.camera_label.image = active
            self.current_frame = frame
        except Exception:
            # Em caso de falha momentânea, manter última imagem (sem limpar)
            pass

    def show_camera_placeholder(self, show: bool = True):
        """Mostrar/ocultar estado vazio na área da câmera."""
        try:
            if show:
                self.camera_label.config(image='', text="👋 Bem-vindo!\nClique em 'Iniciar' para começar a câmera.", style='Camera.TLabel', anchor='center')
                self.camera_label.image = None
            else:
                # Apenas limpa o texto; a próxima renderização colocará a imagem
                self.camera_label.config(text="")
        except Exception:
            pass
    
    def update_recognition_list(self, person_name: str, similarity: float,
                              emotion_data: Dict, age_gender_data: Dict):
        """Atualizar tabela de reconhecimentos (com filtro)."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        show_emotion = getattr(self.multi_system, 'enable_emotion', True) if self.multi_system else getattr(self, '_show_emotions', True)
        show_age_gender = getattr(self.multi_system, 'enable_age_gender', True) if self.multi_system else getattr(self, '_show_age_gender', True)

        row = {
            'Hora': timestamp,
            'Pessoa': person_name,
            'Confiança': f"{similarity:.2f}",
            'Emoção': str(emotion_data.get('emotion', 'N/A')) if show_emotion else '-',
            'Idade': str(age_gender_data.get('age', 0)) if show_age_gender else '-',
            'Gênero': str(age_gender_data.get('gender', 'N/A')) if show_age_gender else '-'
        }

        # Armazenar e manter apenas os últimos 200
        self.recognition_events.insert(0, row)
        if len(self.recognition_events) > 200:
            self.recognition_events = self.recognition_events[:200]

        self.refresh_recognition_table()

    def refresh_recognition_table(self):
        """Reaplicar filtro e preencher a tabela de reconhecimentos."""
        if not hasattr(self, 'recognition_tree'):
            return
        # Limpar atual
        for item in self.recognition_tree.get_children():
            self.recognition_tree.delete(item)
        query = (self.recognition_filter_var.get() or '').strip().lower() if hasattr(self, 'recognition_filter_var') else ''
        for ev in self.recognition_events:
            if query and not any(query in str(v).lower() for v in ev.values()):
                continue
            self.recognition_tree.insert('', 'end', values=(ev['Hora'], ev['Pessoa'], ev['Confiança'], ev['Emoção'], ev['Idade'], ev['Gênero']))

    # -------- Visualização: fullscreen e zoom --------
    def toggle_fullscreen(self):
        self.is_fullscreen = not self.is_fullscreen
        try:
            self.root.attributes('-fullscreen', self.is_fullscreen)
        except Exception:
            # Fallback: maximizar
            try:
                self.root.state('zoomed' if self.is_fullscreen else 'normal')
            except Exception:
                pass

    def exit_fullscreen(self):
        if self.is_fullscreen:
            self.is_fullscreen = False
            try:
                self.root.attributes('-fullscreen', False)
            except Exception:
                try:
                    self.root.state('normal')
                except Exception:
                    pass

    def set_ui_scale(self, factor: float):
        """Ajustar escala da interface para melhorar legibilidade."""
        try:
            # Limitar escala para evitar valores extremos
            factor = float(factor)
            if factor < 0.75:
                factor = 0.75
            if factor > 2.5:
                factor = 2.5
            self._ui_scale = factor
            self.root.tk.call('tk', 'scaling', self._ui_scale)
        except Exception:
            pass

    def change_ui_scale(self, delta: float):
        """Incrementar/diminuir a escala da UI (atalhos)."""
        try:
            self.set_ui_scale(self._ui_scale + float(delta))
        except Exception:
            pass

    def reset_ui_scale(self):
        """Restaurar escala padrão confortável."""
        self.set_ui_scale(1.25)

    def reset_layout(self):
        """Restaurar layout padrão (zoom e divisor)."""
        try:
            self.reset_ui_scale()
            # Reposicionar divisor principal (usa a mesma rotina robusta)
            self.root.after(50, self._post_init_layout_fix)
            # Garantir que a área de câmera tenha conteúdo visível
            try:
                if not self.is_running:
                    # Mostrar placeholder quando não estiver rodando
                    self.show_camera_placeholder(True)
                else:
                    # Se estiver rodando, reiniciar pipeline para reanexar exibição
                    self.restart_cameras()
            except Exception:
                pass
        except Exception:
            pass

    def restart_cameras(self):
        """Reiniciar pipeline de câmeras preservando quais estavam ativas."""
        try:
            prev_running = {name for name, st in self.camera_states.items() if st.get('running')}
            # Parar tudo (sem alterar flags do arquivo de config)
            try:
                self.stop_multi_camera()
            except Exception:
                pass
            # Iniciar novamente com base em camera_settings.json
            self.start_multi_camera()
            # Ajustar estados para refletir o que estava ativo antes
            for cam in list(self.camera_states.keys()):
                if cam not in prev_running and self.camera_states[cam]['running']:
                    # Desligar câmeras que não deveriam estar rodando
                    try:
                        self.stop_camera(cam)
                    except Exception:
                        pass
            # Forçar atualização visual
            self.update_statistics()
        except Exception as e:
            logger.error(f"Erro ao reiniciar câmeras: {e}")
    
    def update_attendance_list(self):
        """Atualizar lista de presenças"""
        # Limpar lista atual
        for item in self.attendance_tree.get_children():
            self.attendance_tree.delete(item)
        
        # Adicionar presenças de hoje
        today_attendance = self.attendance_tracker.get_today_attendance()
        for record in reversed(today_attendance[-20:]):  # Últimas 20
            time_str = datetime.fromisoformat(record['timestamp']).strftime("%H:%M:%S")
            self.attendance_tree.insert('', 'end', values=(
                time_str,
                record['person'],
                f"{record['confidence']:.2f}"
            ))

    def update_attendance_summary(self):
        """Atualizar resumo por pessoa (última vista, média de confiança, registros hoje)."""
        # Limpar lista atual
        if not hasattr(self, 'attendance_summary_tree'):
            return
        for item in self.attendance_summary_tree.get_children():
            self.attendance_summary_tree.delete(item)

        try:
            today_attendance = self.attendance_tracker.get_today_attendance()
            # Agrupar por pessoa
            grouped = {}
            for r in today_attendance:
                grouped.setdefault(r['person'], []).append(r)

            # Montar linhas com estatísticas globais e contagem de hoje
            rows = []
            for person, records in grouped.items():
                stats = self.attendance_tracker.get_person_statistics(person)
                last_seen = stats.get('last_seen')
                last_seen_fmt = datetime.fromisoformat(last_seen).strftime('%H:%M:%S') if last_seen else '-'
                avg_conf = stats.get('average_confidence', 0.0)
                rows.append((person, last_seen_fmt, f"{avg_conf:.2f}", len(records)))

            # Ordenar por última vista desc
            def sort_key(row):
                ts = row[1]
                try:
                    return datetime.strptime(ts, '%H:%M:%S')
                except Exception:
                    return datetime.fromtimestamp(0)
            rows.sort(key=sort_key, reverse=True)

            for row in rows:
                self.attendance_summary_tree.insert('', 'end', values=row)
        except Exception as e:
            logger.error(f"Erro ao atualizar resumo de presença: {e}")
    
    def update_notifications(self):
        """Atualizar lista de notificações"""
        # Limpar lista atual
        self.notifications_listbox.delete(0, tk.END)
        
        # Adicionar notificações recentes
        recent_notifications = self.notification_system.get_recent_notifications(20)
        for notification in reversed(recent_notifications):
            timestamp = datetime.fromisoformat(notification['timestamp']).strftime("%H:%M:%S")
            text = f"[{timestamp}] {notification['icon']} {notification['message']}"
            self.notifications_listbox.insert(0, text)
    
    def update_statistics(self):
        """Atualizar estatísticas em tempo real"""
        # Preferir estatísticas do sistema multi-câmeras, se ativo
        if self.multi_system is not None:
            try:
                stats = self.multi_system.get_system_stats()
                def _set_multi(stats=stats):
                    self.fps_label.config(text=f"FPS: {stats.get('fps', 0):.1f}")
                    self.memory_label.config(text=f"Memória: {stats.get('memory_usage', 0):.1f}%")
                    self.recognitions_label.config(text=f"Reconhecimentos: {stats.get('total_recognitions', 0)}")
                    today_attendance = len(self.attendance_tracker.get_today_attendance())
                    self.attendance_label.config(text=f"Presenças hoje: {today_attendance}")
                self._ui_call(_set_multi)
            except Exception as e:
                logger.error(f"Erro ao obter estatísticas do sistema multi-câmeras: {e}")
        elif self.system:
            try:
                status = self.system.get_system_status()
                def _set_stats(status=status):
                    self.fps_label.config(text=f"FPS: {status['fps']:.1f}")
                    self.memory_label.config(text=f"Memória: {status['memory_usage_mb']:.1f} MB")
                    rec_stats = status['recognition_stats']
                    self.recognitions_label.config(text=f"Reconhecimentos: {rec_stats['total_recognitions']}")
                    today_attendance = len(self.attendance_tracker.get_today_attendance())
                    self.attendance_label.config(text=f"Presenças hoje: {today_attendance}")
                self._ui_call(_set_stats)
            except Exception as e:
                logger.error(f"Erro ao obter estatísticas do sistema: {e}")

    # ------------------
    # Configuração de Câmeras
    # ------------------
    def load_camera_settings(self):
        """Carregar configurações de câmera de arquivo e aplicar no ambiente."""
        self.camera_config = {
            'webcam_enabled': True,
            'c100_enabled': False,
            'c100_url': ''
        }
        try:
            if os.path.exists('camera_settings.json'):
                with open('camera_settings.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.camera_config.update({
                        'webcam_enabled': bool(data.get('webcam_enabled', True)),
                        'c100_enabled': bool(data.get('c100_enabled', False)),
                        'c100_url': data.get('c100_url', '')
                    })
            # Aplicar variável de ambiente para sessão atual
            if self.camera_config.get('c100_url'):
                os.environ['RTSP_C100_URL'] = self.camera_config['c100_url']
        except Exception as e:
            logger.error(f"Erro ao carregar configuração de câmeras: {e}")

    def open_camera_config_dialog(self):
        """Abrir diálogo simples para configurar câmeras."""
        dialog = tk.Toplevel(self.root)
        dialog.title('Configuração de Câmeras')
        dialog.grab_set()

        webcam_var = tk.BooleanVar(value=self.camera_config.get('webcam_enabled', True))
        c100_enable_var = tk.BooleanVar(value=self.camera_config.get('c100_enabled', False))
        c100_url_var = tk.StringVar(value=self.camera_config.get('c100_url', os.getenv('RTSP_C100_URL', '')))

        ttk.Checkbutton(dialog, text='Habilitar Webcam (local 0)', variable=webcam_var).grid(row=0, column=0, columnspan=2, sticky='w', padx=10, pady=10)
        ttk.Checkbutton(dialog, text='Habilitar C100 (RTSP)', variable=c100_enable_var).grid(row=1, column=0, columnspan=2, sticky='w', padx=10)
        ttk.Label(dialog, text='RTSP C100 URL:').grid(row=2, column=0, sticky='e', padx=10, pady=10)
        ttk.Entry(dialog, textvariable=c100_url_var, width=60).grid(row=2, column=1, sticky='w', padx=10)

        def save_and_close():
            cfg = {
                'webcam_enabled': bool(webcam_var.get()),
                'c100_enabled': bool(c100_enable_var.get()),
                'c100_url': c100_url_var.get().strip()
            }
            try:
                with open('camera_settings.json', 'w', encoding='utf-8') as f:
                    json.dump(cfg, f, indent=2, ensure_ascii=False)
                self.camera_config = cfg
                # Aplicar env var nesta sessão
                if cfg.get('c100_url'):
                    os.environ['RTSP_C100_URL'] = cfg['c100_url']
                self.notification_system.add_notification('info', 'Configurações de câmera salvas')
                self.update_notifications()
            except Exception as e:
                messagebox.showerror('Erro', f'Falha ao salvar configurações: {e}')
            finally:
                dialog.destroy()

        btn_frame = ttk.Frame(dialog)
        btn_frame.grid(row=3, column=0, columnspan=2, pady=10)
        ttk.Button(btn_frame, text='Salvar', command=save_and_close).pack(side='left', padx=5)
        ttk.Button(btn_frame, text='Cancelar', command=dialog.destroy).pack(side='left', padx=5)

    
    def capture_frame(self):
        """Capturar frame atual"""
        if self.current_frame is not None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"capture_{timestamp}.jpg"
            cv2.imwrite(filename, self.current_frame)
            self.notification_system.add_notification('info', f'Frame salvo: {filename}')
            self.update_notifications()
    
    def add_person(self):
        """Adicionar nova pessoa ao sistema"""
        dialog = tk.Toplevel(self.root)
        dialog.title('Adicionar Pessoa')
        dialog.grab_set()

        name_var = tk.StringVar()
        path_var = tk.StringVar()

        ttk.Label(dialog, text='Nome:').grid(row=0, column=0, sticky='e', padx=10, pady=10)
        ttk.Entry(dialog, textvariable=name_var, width=40).grid(row=0, column=1, sticky='w')
        ttk.Label(dialog, text='Imagem:').grid(row=1, column=0, sticky='e', padx=10, pady=10)
        ttk.Entry(dialog, textvariable=path_var, width=40).grid(row=1, column=1, sticky='w')

        def browse():
            filename = filedialog.askopenfilename(filetypes=[('Imagens', '*.jpg;*.jpeg;*.png;*.bmp;*.tiff')])
            if filename:
                path_var.set(filename)
        ttk.Button(dialog, text='Escolher...', command=browse).grid(row=1, column=2, padx=5)

        def save():
            name = name_var.get().strip()
            img_path = path_var.get().strip()
            if not name or not img_path or not os.path.exists(img_path):
                messagebox.showerror('Erro', 'Informe um nome e uma imagem válidos')
                return
            try:
                # Copiar imagem para pasta known_faces com nome padronizado
                os.makedirs(self.config.known_faces_dir, exist_ok=True)
                ext = os.path.splitext(img_path)[1].lower() or '.jpg'
                dest = os.path.join(self.config.known_faces_dir, f"{name}{ext}")
                import shutil as _sh
                _sh.copyfile(img_path, dest)

                # Adicionar ao(s) reconhecedor(es)
                target_recognizers = []
                if self.system:
                    target_recognizers.append(self.system.face_recognizer)
                if self.multi_system:
                    target_recognizers.append(self.multi_system.face_recognizer)

                img = cv2.imread(dest)
                ok_any = False
                for recog in target_recognizers:
                    try:
                        if recog.add_person(name, img):
                            ok_any = True
                    except Exception:
                        pass
                self.notification_system.add_notification('info', f'Pessoa {name} adicionada')
                self.update_notifications()
                dialog.destroy()
            except Exception as e:
                messagebox.showerror('Erro', f'Falha ao adicionar pessoa: {e}')

        btns = ttk.Frame(dialog)
        btns.grid(row=2, column=0, columnspan=3, pady=10)
        ttk.Button(btns, text='Salvar', command=save).pack(side='left', padx=5)
        ttk.Button(btns, text='Cancelar', command=dialog.destroy).pack(side='left', padx=5)
    
    def manage_people(self):
        """Gerenciar pessoas conhecidas"""
        top = tk.Toplevel(self.root)
        top.title('Pessoas Conhecidas')
        top.geometry('400x300')
        top.grab_set()

        # Consolidar lista a partir de qualquer sistema ativo
        people = set()
        if self.system:
            people.update(self.system.face_recognizer.known_embeddings.keys())
        if self.multi_system:
            people.update(self.multi_system.face_recognizer.known_embeddings.keys())

        lb = tk.Listbox(top)
        for p in sorted(people):
            lb.insert(tk.END, p)
        lb.pack(fill='both', expand=True, padx=10, pady=10)

        def remove_selected():
            sel = lb.curselection()
            if not sel:
                return
            name = lb.get(sel[0])
            removed = False
            try:
                if self.system and self.system.face_recognizer.remove_person(name):
                    removed = True
                if self.multi_system and self.multi_system.face_recognizer.remove_person(name):
                    removed = True
                # Remover arquivo da pasta known_faces se existir
                for ext in ('.jpg', '.jpeg', '.png', '.bmp', '.tiff'):
                    path = os.path.join(self.config.known_faces_dir, f"{name}{ext}")
                    if os.path.exists(path):
                        try:
                            os.remove(path)
                        except Exception:
                            pass
                if removed:
                    lb.delete(sel[0])
                    self.notification_system.add_notification('info', f'Pessoa {name} removida')
                    self.update_notifications()
            except Exception as e:
                messagebox.showerror('Erro', f'Falha ao remover: {e}')

        btns = ttk.Frame(top)
        btns.pack(pady=5)
        ttk.Button(btns, text='Remover Selecionado', command=remove_selected).pack(side='left', padx=5)
        ttk.Button(btns, text='Fechar', command=top.destroy).pack(side='left', padx=5)
    
    def export_data(self):
        """Exportar dados do sistema"""
        filename = filedialog.asksaveasfilename(
            defaultextension=".zip",
            filetypes=[("ZIP", "*.zip"), ("All files", "*.*")]
        )
        if filename:
            try:
                import zipfile as _zf
                with _zf.ZipFile(filename, 'w', compression=_zf.ZIP_DEFLATED) as zf:
                    # Embeddings
                    if os.path.exists(self.config.embeddings_file):
                        zf.write(self.config.embeddings_file)
                    # Attendance
                    if os.path.exists('attendance.json'):
                        zf.write('attendance.json')
                    # Known faces
                    if os.path.isdir(self.config.known_faces_dir):
                        for root, _, files in os.walk(self.config.known_faces_dir):
                            for f in files:
                                zf.write(os.path.join(root, f))
                self.notification_system.add_notification('info', f'Dados exportados: {filename}')
                self.update_notifications()
            except Exception as e:
                messagebox.showerror('Erro', f'Falha ao exportar: {e}')
    
    def import_data(self):
        """Importar dados para o sistema"""
        filename = filedialog.askopenfilename(
            filetypes=[("ZIP", "*.zip"), ("All files", "*.*")]
        )
        if filename:
            try:
                import zipfile as _zf
                with _zf.ZipFile(filename, 'r') as zf:
                    zf.extractall('.')
                # Recarregar embeddings
                if self.system:
                    try:
                        self.system.face_recognizer.reload_embeddings()
                    except Exception:
                        pass
                if self.multi_system:
                    try:
                        self.multi_system.face_recognizer.reload_embeddings()
                    except Exception:
                        pass
                self.notification_system.add_notification('info', f'Dados importados de: {filename}')
                self.update_notifications()
            except Exception as e:
                messagebox.showerror('Erro', f'Falha ao importar: {e}')
    
    def toggle_fps(self):
        """Alternar exibição de FPS"""
        self.config.ui.show_fps = not self.config.ui.show_fps
        messagebox.showinfo("Configuração", f"FPS {'habilitado' if self.config.ui.show_fps else 'desabilitado'}")
    
    def toggle_confidence(self):
        """Alternar exibição de confiança"""
        self.config.ui.show_confidence = not self.config.ui.show_confidence
        messagebox.showinfo("Configuração", f"Confiança {'habilitada' if self.config.ui.show_confidence else 'desabilitada'}")
    
    def toggle_emotions(self):
        """Alternar exibição de emoções"""
        # Alternar flag de emoções no sistema multi-câmeras, se disponível
        if self.multi_system is not None:
            self.multi_system.enable_emotion = not getattr(self.multi_system, 'enable_emotion', True)
            state = 'habilitadas' if self.multi_system.enable_emotion else 'desabilitadas'
            self.notification_system.add_notification('info', f'Emoções {state}')
            self.update_notifications()
        else:
            # Alternar em nível de GUI (fallback)
            if not hasattr(self, '_show_emotions'):
                self._show_emotions = True
            self._show_emotions = not self._show_emotions
            state = 'habilitadas' if self._show_emotions else 'desabilitadas'
            self.notification_system.add_notification('info', f'Emoções {state} (GUI)')
            self.update_notifications()

    def toggle_age_gender(self):
        """Alternar exibição de idade/gênero"""
        if self.multi_system is not None:
            self.multi_system.enable_age_gender = not getattr(self.multi_system, 'enable_age_gender', True)
            state = 'habilitado' if self.multi_system.enable_age_gender else 'desabilitado'
            self.notification_system.add_notification('info', f'Idade/Gênero {state}')
            self.update_notifications()
        else:
            if not hasattr(self, '_show_age_gender'):
                self._show_age_gender = True
            self._show_age_gender = not self._show_age_gender
            state = 'habilitado' if self._show_age_gender else 'desabilitado'
            self.notification_system.add_notification('info', f'Idade/Gênero {state} (GUI)')
            self.update_notifications()
    
    def attendance_report(self):
        """Gerar relatório de presença"""
        # Implementar relatório de presença
        messagebox.showinfo("Funcionalidade", "Relatório de presença será implementado")
    
    def analytics_report(self):
        """Gerar relatório de analytics"""
        # Implementar relatório de analytics
        messagebox.showinfo("Funcionalidade", "Relatório de analytics será implementado")
    
    def export_analytics(self):
        """Exportar analytics"""
        filename = self.analytics.export_analytics()
        self.notification_system.add_notification('info', f'Analytics exportados: {filename}')
        self.update_notifications()
    
    def apply_settings(self):
        """Aplicar configurações"""
        self.config.model.name = self.model_var.get()
        self.config.model.threshold = self.threshold_var.get()
        self.config.model.detector_backend = self.detector_var.get()
        # Reinicializar sistema com novas configurações
        if self.system:
            self.system.stop_processing_thread()
        self.initialize_system()
        self.notification_system.add_notification('info', f'Modelo aplicado: {self.config.model.name}')
        self.update_notifications()

    def on_close(self):
        """Finalizar recursos e fechar a aplicação com segurança."""
        try:
            self.is_running = False
            if self.multi_system:
                self.multi_system.stop()
                self.multi_system = None
            if self.system:
                try:
                    self.system.stop_processing_thread()
                except Exception:
                    pass
        finally:
            self.root.destroy()
    
    def show_about(self):
        """Mostrar informações sobre o sistema"""
        about_text = """
Sistema de Reconhecimento Facial v2.0

Funcionalidades:
• Reconhecimento facial em tempo real
• Detecção de emoções
• Detecção de idade e gênero
• Controle de presença
• Analytics avançados
• Interface gráfica moderna

Desenvolvido com Python, OpenCV e DeepFace
        """
        messagebox.showinfo("Sobre", about_text)
    
    def show_documentation(self):
        """Mostrar documentação"""
        messagebox.showinfo("Documentação", "Consulte o arquivo README.md para documentação completa")
    
    def run(self):
        """Executar interface"""
        self.root.mainloop()

def main():
    """Função principal da interface gráfica"""
    try:
        app = ModernFaceRecognitionGUI()
        app.run()
    except Exception as e:
        logger.error(f"Erro na interface gráfica: {e}")
        messagebox.showerror("Erro", f"Erro fatal na interface: {str(e)}")

if __name__ == "__main__":
    main()
