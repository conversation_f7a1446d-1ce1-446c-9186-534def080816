"""
Funcionalidades avançadas para o sistema de reconhecimento facial
"""
import cv2
import numpy as np
import json
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict, deque
import threading
import time
from logger import logger

class EmotionDetector:
    """Detector de emoções usando DeepFace"""
    
    def __init__(self):
        self.emotions = ['angry', 'disgust', 'fear', 'happy', 'sad', 'surprise', 'neutral']
        self.emotion_colors = {
            'angry': (0, 0, 255),      # Ver<PERSON>ho
            'disgust': (0, 128, 255),  # Laranja
            'fear': (128, 0, 255),     # Roxo
            'happy': (0, 255, 0),      # Verde
            'sad': (255, 0, 0),        # Azul
            'surprise': (0, 255, 255), # <PERSON>elo
            'neutral': (128, 128, 128) # Cinza
        }
    
    def initialize(self):
        """Inicializar detector de emoções"""
        logger.info("Detector de emoções inicializado")
        return True
    
    def detect_emotion(self, face_image: np.ndarray) -> Dict[str, Any]:
        """Detectar emoção em uma face"""
        try:
            from deepface import DeepFace
            
            result = DeepFace.analyze(
                img_path=face_image,
                actions=['emotion'],
                enforce_detection=False
            )
            
            if result and len(result) > 0:
                emotions = result[0]['emotion']
                dominant_emotion = max(emotions, key=emotions.get)
                confidence = emotions[dominant_emotion]
                
                return {
                    'emotion': dominant_emotion,
                    'confidence': confidence,
                    'all_emotions': emotions,
                    'color': self.emotion_colors.get(dominant_emotion, (255, 255, 255))
                }
            else:
                return {
                    'emotion': 'neutral',
                    'confidence': 0.0,
                    'all_emotions': {},
                    'color': (128, 128, 128)
                }
                
        except Exception as e:
            logger.error(f"Erro na detecção de emoção: {e}")
            return {
                'emotion': 'neutral',
                'confidence': 0.0,
                'all_emotions': {},
                'color': (128, 128, 128)
            }

class AgeGenderDetector:
    """Detector de idade e gênero"""
    
    def __init__(self):
        self.gender_colors = {
            'Man': (255, 0, 0),    # Azul
            'Woman': (255, 0, 255) # Magenta
        }
    
    def initialize(self):
        """Inicializar detector de idade e gênero"""
        logger.info("Detector de idade e gênero inicializado")
        return True
    
    def detect_age_gender(self, face_image: np.ndarray) -> Dict[str, Any]:
        """Detectar idade e gênero"""
        try:
            from deepface import DeepFace
            
            result = DeepFace.analyze(
                img_path=face_image,
                actions=['age', 'gender'],
                enforce_detection=False
            )
            
            if result and len(result) > 0:
                age = result[0]['age']
                gender = result[0]['dominant_gender']
                
                return {
                    'age': age,
                    'gender': gender,
                    'age_range': self._get_age_range(age),
                    'color': self.gender_colors.get(gender, (255, 255, 255))
                }
            else:
                return {
                    'age': 0,
                    'gender': 'Unknown',
                    'age_range': 'Unknown',
                    'color': (128, 128, 128)
                }
                
        except Exception as e:
            logger.error(f"Erro na detecção de idade/gênero: {e}")
            return {
                'age': 0,
                'gender': 'Unknown',
                'age_range': 'Unknown',
                'color': (128, 128, 128)
            }
    
    def _get_age_range(self, age: int) -> str:
        """Obter faixa etária"""
        if age < 18:
            return "Criança/Adolescente"
        elif age < 30:
            return "Jovem Adulto"
        elif age < 50:
            return "Adulto"
        elif age < 65:
            return "Meia Idade"
        else:
            return "Idoso"

class AttendanceTracker:
    """Sistema de controle de presença"""
    
    def __init__(self, attendance_file: str = "attendance.json"):
        self.attendance_file = attendance_file
        self.attendance_data = self._load_attendance()
        self.presence_threshold = 5  # Segundos para considerar presença
        self.presence_records = defaultdict(lambda: deque(maxlen=10))  # Últimas 10 detecções
    
    def initialize(self):
        """Inicializar tracker de presença"""
        logger.info("Tracker de presença inicializado")
        return True
    
    def _load_attendance(self) -> Dict[str, Any]:
        """Carregar dados de presença"""
        if os.path.exists(self.attendance_file):
            try:
                with open(self.attendance_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.error(f"Erro ao carregar presença: {e}")
        return {'records': [], 'statistics': {}}
    
    def _save_attendance(self):
        """Salvar dados de presença"""
        try:
            with open(self.attendance_file, 'w', encoding='utf-8') as f:
                json.dump(self.attendance_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Erro ao salvar presença: {e}")
    
    def record_presence(self, person_name: str, confidence: float):
        """Registrar presença de uma pessoa"""
        current_time = datetime.now()
        
        # Adicionar detecção atual
        self.presence_records[person_name].append({
            'timestamp': current_time.isoformat(),
            'confidence': confidence
        })
        
        # Verificar se deve registrar presença oficial
        if self._should_record_presence(person_name):
            self._add_attendance_record(person_name, current_time, confidence)
    
    def _should_record_presence(self, person_name: str) -> bool:
        """Verificar se deve registrar presença oficial"""
        records = self.presence_records[person_name]
        if len(records) < 3:  # Mínimo de 3 detecções
            return False
        
        # Verificar se as últimas detecções são recentes e consistentes
        recent_records = list(records)[-3:]
        current_time = datetime.now()
        
        for record in recent_records:
            record_time = datetime.fromisoformat(record['timestamp'])
            if (current_time - record_time).total_seconds() > 10:  # Mais de 10 segundos
                return False
        
        # Verificar se já foi registrado hoje
        today = current_time.date().isoformat()
        for record in self.attendance_data['records']:
            if (record['person'] == person_name and 
                record['date'] == today and
                (current_time - datetime.fromisoformat(record['timestamp'])).total_seconds() < 300):  # 5 minutos
                return False
        
        return True
    
    def _add_attendance_record(self, person_name: str, timestamp: datetime, confidence: float):
        """Adicionar registro de presença"""
        record = {
            'person': person_name,
            'timestamp': timestamp.isoformat(),
            'date': timestamp.date().isoformat(),
            'time': timestamp.time().isoformat(),
            'confidence': confidence
        }
        
        self.attendance_data['records'].append(record)
        self._update_statistics()
        self._save_attendance()
        
        logger.info(f"Presença registrada: {person_name} em {timestamp.strftime('%d/%m/%Y %H:%M:%S')}")
    
    def _update_statistics(self):
        """Atualizar estatísticas de presença"""
        records = self.attendance_data['records']
        stats = {}
        
        for record in records:
            person = record['person']
            date = record['date']
            
            if person not in stats:
                stats[person] = {}
            
            if date not in stats[person]:
                stats[person][date] = 0
            
            stats[person][date] += 1
        
        self.attendance_data['statistics'] = stats
    
    def get_today_attendance(self) -> List[Dict[str, Any]]:
        """Obter presenças de hoje"""
        today = datetime.now().date().isoformat()
        return [r for r in self.attendance_data['records'] if r['date'] == today]
    
    def get_person_statistics(self, person_name: str) -> Dict[str, Any]:
        """Obter estatísticas de uma pessoa"""
        person_records = [r for r in self.attendance_data['records'] if r['person'] == person_name]
        
        if not person_records:
            return {'total_days': 0, 'last_seen': None, 'average_confidence': 0.0}
        
        total_days = len(set(r['date'] for r in person_records))
        last_seen = max(person_records, key=lambda x: x['timestamp'])
        avg_confidence = sum(r['confidence'] for r in person_records) / len(person_records)
        
        return {
            'total_days': total_days,
            'last_seen': last_seen['timestamp'],
            'average_confidence': avg_confidence,
            'total_records': len(person_records)
        }

class NotificationSystem:
    """Sistema de notificações"""
    
    def __init__(self):
        self.notifications = deque(maxlen=100)  # Últimas 100 notificações
        self.notification_types = {
            'recognition': {'color': (0, 255, 0), 'icon': '👤'},
            'unknown': {'color': (0, 0, 255), 'icon': '❓'},
            'attendance': {'color': (255, 255, 0), 'icon': '✅'},
            'error': {'color': (0, 0, 255), 'icon': '❌'},
            'info': {'color': (0, 255, 255), 'icon': 'ℹ️'}
        }
    
    def initialize(self):
        """Inicializar sistema de notificações"""
        logger.info("Sistema de notificações inicializado")
        return True
    
    def add_notification(self, notification_type: str, message: str, person_name: str = None):
        """Adicionar notificação"""
        notification = {
            'type': notification_type,
            'message': message,
            'person_name': person_name,
            'timestamp': datetime.now().isoformat(),
            'color': self.notification_types.get(notification_type, {}).get('color', (255, 255, 255)),
            'icon': self.notification_types.get(notification_type, {}).get('icon', '📢')
        }
        
        self.notifications.append(notification)
        logger.info(f"Notificação {notification_type}: {message}")
    
    def get_recent_notifications(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Obter notificações recentes"""
        return list(self.notifications)[-limit:]
    
    def clear_notifications(self):
        """Limpar notificações"""
        self.notifications.clear()

class VideoRecorder:
    """Gravador de vídeo para eventos importantes"""
    
    def __init__(self, output_dir: str = "recordings"):
        self.output_dir = output_dir
        self.current_recording = None
        self.recording_start_time = None
        self.max_recording_duration = 60  # 60 segundos máximo
    
    def initialize(self):
        """Inicializar gravador de vídeo"""
        # Garantir diretório de saída
        try:
            os.makedirs(self.output_dir, exist_ok=True)
        except Exception as e:
            logger.error(f"Erro ao criar diretório de gravação: {e}")
        logger.info("Gravador de vídeo inicializado")
        return True
    
    def finalize(self):
        """Finalizar gravador de vídeo"""
        logger.info("Gravador de vídeo finalizado")
        return True

    def record_frame(self, frame: np.ndarray, person_name: str = None, confidence: float = None,
                     event_type: str = 'recognition'):
        """Compatibilidade: inicia (se necessário) e grava um frame."""
        if self.current_recording is None:
            try:
                self.start_recording(frame, event_type=event_type, person_name=person_name)
            except Exception as e:
                logger.error(f"Erro ao iniciar gravação: {e}")
                return
        try:
            self.add_frame(frame)
        except Exception as e:
            logger.error(f"Erro ao gravar frame: {e}")
    
    def start_recording(self, frame: np.ndarray, event_type: str, person_name: str = None):
        """Iniciar gravação"""
        if self.current_recording is not None:
            return  # Já gravando
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{event_type}_{person_name or 'unknown'}_{timestamp}.mp4"
        filepath = os.path.join(self.output_dir, filename)
        
        # Configurar codec de vídeo
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        fps = 30
        height, width = frame.shape[:2]
        
        self.current_recording = cv2.VideoWriter(filepath, fourcc, fps, (width, height))
        self.recording_start_time = time.time()
        
        logger.info(f"Iniciando gravação: {filepath}")
    
    def add_frame(self, frame: np.ndarray):
        """Adicionar frame à gravação"""
        if self.current_recording is not None:
            self.current_recording.write(frame)
            
            # Verificar duração máxima
            if time.time() - self.recording_start_time > self.max_recording_duration:
                self.stop_recording()
    
    def stop_recording(self):
        """Parar gravação"""
        if self.current_recording is not None:
            self.current_recording.release()
            self.current_recording = None
            self.recording_start_time = None
            logger.info("Gravação finalizada")

class AdvancedAnalytics:
    """Analytics avançados do sistema"""
    
    def __init__(self):
        self.analytics_data = {
            'daily_stats': defaultdict(lambda: defaultdict(int)),
            'hourly_stats': defaultdict(lambda: defaultdict(int)),
            'emotion_stats': defaultdict(lambda: defaultdict(int)),
            'age_gender_stats': defaultdict(lambda: defaultdict(int)),
            'recognition_accuracy': deque(maxlen=1000)
        }
    
    def initialize(self):
        """Inicializar analytics"""
        logger.info("Analytics inicializado")
    
    def finalize(self):
        """Finalizar analytics"""
        logger.info("Analytics finalizado")
    
    def record_recognition(self, person_name: str, confidence: float, emotion: str = None, 
                          age: int = None, gender: str = None):
        """Registrar reconhecimento para analytics"""
        current_time = datetime.now()
        date_key = current_time.date().isoformat()
        hour_key = current_time.hour
        
        # Estatísticas diárias
        self.analytics_data['daily_stats'][date_key]['total_recognitions'] += 1
        self.analytics_data['daily_stats'][date_key][person_name] += 1
        
        # Estatísticas por hora
        self.analytics_data['hourly_stats'][hour_key]['total_recognitions'] += 1
        self.analytics_data['hourly_stats'][hour_key][person_name] += 1
        
        # Estatísticas de emoção
        if emotion:
            self.analytics_data['emotion_stats'][person_name][emotion] += 1
        
        # Estatísticas de idade/gênero
        if age and gender:
            age_group = self._get_age_group(age)
            self.analytics_data['age_gender_stats'][age_group][gender] += 1
        
        # Precisão do reconhecimento
        self.analytics_data['recognition_accuracy'].append(confidence)
    
    def _get_age_group(self, age: int) -> str:
        """Obter grupo etário"""
        if age < 18:
            return "0-17"
        elif age < 30:
            return "18-29"
        elif age < 50:
            return "30-49"
        elif age < 65:
            return "50-64"
        else:
            return "65+"
    
    def get_daily_report(self, date: str = None) -> Dict[str, Any]:
        """Obter relatório diário"""
        if date is None:
            date = datetime.now().date().isoformat()
        
        daily_data = self.analytics_data['daily_stats'][date]
        
        return {
            'date': date,
            'total_recognitions': daily_data.get('total_recognitions', 0),
            'unique_people': len([k for k in daily_data.keys() if k != 'total_recognitions']),
            'top_person': max(daily_data.items(), key=lambda x: x[1] if x[0] != 'total_recognitions' else 0),
            'average_confidence': np.mean(list(self.analytics_data['recognition_accuracy'])) if self.analytics_data['recognition_accuracy'] else 0
        }
    
    def get_hourly_heatmap(self) -> Dict[int, int]:
        """Obter mapa de calor por hora"""
        hourly_data = {}
        for hour in range(24):
            hourly_data[hour] = self.analytics_data['hourly_stats'][hour].get('total_recognitions', 0)
        return hourly_data
    
    def get_emotion_distribution(self) -> Dict[str, int]:
        """Obter distribuição de emoções"""
        emotion_dist = defaultdict(int)
        for person_emotions in self.analytics_data['emotion_stats'].values():
            for emotion, count in person_emotions.items():
                emotion_dist[emotion] += count
        return dict(emotion_dist)
    
    def export_analytics(self, filename: str = None) -> str:
        """Exportar analytics para arquivo"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"analytics_{timestamp}.json"
        
        # Converter defaultdicts para dicts normais
        export_data = {}
        for key, value in self.analytics_data.items():
            if isinstance(value, defaultdict):
                export_data[key] = dict(value)
            elif isinstance(value, deque):
                export_data[key] = list(value)
            else:
                export_data[key] = value
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(export_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Analytics exportados para: {filename}")
        return filename
