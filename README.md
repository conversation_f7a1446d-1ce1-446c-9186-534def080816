# Escola Segura — Face Demo (RTX 3050)

Este projeto entrega um baseline prático para:
- Demo em tempo real (detecção + reconhecimento) via `insightface`.
- Captura de faces para montar seu dataset.
- Construção de galeria de identidades (embeddings médios) e matching por similaridade.

Funciona com sua GPU NVIDIA RTX 3050 6GB, webcam do PC e câmera IP (Intelbras/Tapo C100) via RTSP.

## Requisitos
- Windows 10/11, Python 3.10+ (recomendado 3.10).
- GPU RTX 3050 6GB (CUDA instalado — opcional, melhora o desempenho com `onnxruntime-gpu`).

## Instalação
```powershell
python -m venv .venv
. .venv\Scripts\Activate.ps1
pip install --upgrade pip
pip install -r requirements.txt
```
Observações:
- Se `onnxruntime-gpu` não funcionar, o código cai automaticamente para CPU (mais lento).
- Se já tiver CUDA/cuDNN corretos instalados, `onnxruntime-gpu` vai acelerar a inferência.

## RTSP das Câmeras IP
Verifique o RTSP com o VLC primeiro (Mídia > Abrir Fluxo de Rede):

- Intelbras (Dahua-like, exemplo comum):
  - `rtsp://usuario:senha@IP:554/cam/realmonitor?channel=1&subtype=0` (stream principal)
  - `rtsp://usuario:senha@IP:554/cam/realmonitor?channel=1&subtype=1` (substream)

- Tapo C100 (exemplos conhecidos):
  - `rtsp://usuario:senha@IP:554/stream1` (stream principal)
  - `rtsp://usuario:senha@IP:554/stream2` (substream)

Dica: assegure-se de habilitar RTSP/ONVIF no app do fabricante. Confirme o IP da câmera e credenciais.

## Estrutura
- `src/app/live_demo.py`: demo em tempo real com detecção + reconhecimento.
- `src/tools/capture.py`: captura faces alinhadas e salva em `data/raw_faces/<nome>/`.
- `src/tools/build_gallery.py`: gera `artifacts/gallery.npz` (embeddings médios por identidade).
- `src/face/engine.py`: wrapper do `insightface` (detector + embeddings, GPU/CPU).
- `src/utils/*`: utilitários de paths/similaridade.

Pastas criadas em runtime:
- `data/raw_faces/<nome>/...` — imagens alinhadas 112x112 por identidade.
- `artifacts/gallery.npz` — galeria pronta para matching.

## Uso Rápido
1) Demo com webcam
```powershell
python -m src.app.live_demo --source 0
```

2) Demo com câmera IP
```powershell
python -m src.app.live_demo --source "rtsp://usuario:senha@IP:554/stream1"
```

3) Capturar faces para seu dataset
```powershell
# Captura da webcam para a identidade "Italo"
python -m src.tools.capture --source 0 --name Italo --save-every 10

# Captura via RTSP para "Visitante1"
python -m src.tools.capture --source "rtsp://usuario:senha@IP:554/stream1" --name Visitante1 --save-every 10
```
Dicas:
- Movimente a cabeça (±yaw/pitch), com e sem óculos/iluminação diferente.
- O script salva faces alinhadas (112x112). Evita duplicatas óbvias por similaridade (>0.98).

4) Construir galeria (média por identidade)
```powershell
python -m src.tools.build_gallery --input data/raw_faces --output artifacts/gallery.npz
```

5) Reconhecimento com a galeria
```powershell
python -m src.app.live_demo --source 0 --gallery artifacts/gallery.npz --threshold 0.35
```
- Ajuste `--threshold` após calibração (veja abaixo). Valores usuais: 0.30–0.42.

## Calibração Rápida do Limiar
- Colete pares positivos (mesma pessoa) e negativos (pessoas diferentes) do seu domínio.
- Rode a demo em modo verbose (ou adicione prints) para salvar similaridades.
- Trace um histograma: escolha o threshold que mantenha alto TPR com baixo FPR.
- Como início: 0.35 para `buffalo_l` costuma ir bem; ajuste por câmera/iluminação.

## Desempenho esperado (RTX 3050 6GB)
- 720p: ~20–35 FPS (detector + embeddings) com `onnxruntime-gpu`, `det_size=640`.
- 1080p: ajuste `--resize 1280` ou use substream RTSP (melhora latência).

## Melhorias e Treino
- Reconhecimento (recomendado primeiro): aumente amostras por identidade (20–50 fotos variadas) e reconstua a galeria.
- Detector (opcional): se perder faces do seu domínio, considere fine-tune de detector (ex.: YOLOv8-face) com imagens rotuladas (WIDER FACE + suas).
- Anti-spoofing: adicionar um classificador leve (liveness) antes do reconhecimento para ambiente crítico.

Quer que eu integre um script simples de calibração de threshold e logging de similaridades? Posso incluir em `src/tools/`.
