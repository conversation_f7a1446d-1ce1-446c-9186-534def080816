"""
Sistema de reconhecimento facial otimizado - Versão 2.0
"""
import cv2
import numpy as np
import threading
import queue
import time
import os
import psutil
from typing import List, Dict, Any, Optional, Tuple
from config import SystemConfig, DEFAULT_CONFIG
from logger import logger
from face_detector import FaceDetector
from face_recognizer import FaceRecognizer

class FaceRecognitionSystemV2:
    """Sistema de reconhecimento facial otimizado"""
    
    def __init__(self, config: SystemConfig = None):
        self.config = config or DEFAULT_CONFIG
        
        # Componentes do sistema
        # Usar um limiar mais permissivo para detecção; o limiar de reconhecimento é independente
        self.face_detector = FaceDetector(
            detector_backend=self.config.model.detector_backend,
            confidence_threshold=0.3
        )
        
        self.face_recognizer = FaceRecognizer(
            known_faces_dir=self.config.known_faces_dir,
            embeddings_file=self.config.embeddings_file,
            model_name=self.config.model.name,
            threshold=self.config.model.threshold
        )
        
        # Controle de processamento
        self.processing = False
        self.frame_queue = queue.Queue(maxsize=self.config.processing.max_queue_size)
        self.result_queue = queue.Queue(maxsize=self.config.processing.max_queue_size)
        self.process_thread = None
        
        # Estatísticas de performance
        self.fps_counter = 0
        self.fps_start_time = time.time()
        self.current_fps = 0.0
        self.last_results = []
        
        # Controle de frames
        self.frame_count = 0
        
        logger.info("Sistema de reconhecimento facial inicializado")
    
    def start_processing_thread(self):
        """Iniciar thread de processamento"""
        if self.processing:
            return
        
        self.processing = True
        self.process_thread = threading.Thread(target=self._process_frames_async, daemon=True)
        self.process_thread.start()
        logger.info("Thread de processamento iniciada")
    
    def stop_processing_thread(self):
        """Parar thread de processamento"""
        self.processing = False
        if self.process_thread:
            self.process_thread.join(timeout=2)
        logger.info("Thread de processamento parada")
    
    def _process_frames_async(self):
        """Processar frames de forma assíncrona"""
        while self.processing:
            try:
                if not self.frame_queue.empty():
                    frame = self.frame_queue.get(timeout=1)
                    
                    # Detectar faces
                    detected_faces = self.face_detector.detect_faces(frame)
                    
                    if detected_faces:
                        # Extrair embeddings
                        embeddings = []
                        for face_data in detected_faces:
                            if 'face_array' in face_data:
                                embedding = self.face_recognizer.embedding_extractor.extract_embedding(
                                    face_data['face_array']
                                )
                                if embedding is not None:
                                    embeddings.append(embedding)
                            else:
                                # Fallback: extrair região da face
                                bbox = face_data['bbox']
                                face_region = self.face_detector.extract_face_region(frame, bbox)
                                embedding = self.face_recognizer.embedding_extractor.extract_embedding(face_region)
                                if embedding is not None:
                                    embeddings.append(embedding)
                        
                        # Reconhecer faces
                        if embeddings:
                            recognition_results = self.face_recognizer.recognize_faces_batch(embeddings)
                            
                            # Combinar resultados de detecção e reconhecimento
                            results = []
                            for i, (face_data, (name, similarity, proc_time)) in enumerate(zip(detected_faces, recognition_results)):
                                results.append({
                                    'name': name,
                                    'similarity': similarity,
                                    'bbox': face_data['bbox'],
                                    'confidence': face_data.get('confidence', 1.0),
                                    'processing_time': proc_time
                                })
                            
                            # Adicionar resultado à queue
                            if not self.result_queue.full():
                                self.result_queue.put(results)
                    else:
                        # Nenhuma face detectada
                        if not self.result_queue.full():
                            self.result_queue.put([])
                
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"Erro no processamento assíncrono: {e}")
    
    def process_frame(self, frame: np.ndarray) -> List[Dict[str, Any]]:
        """Processar um frame individual"""
        # Adicionar frame à queue se não estiver cheia
        if not self.frame_queue.full():
            self.frame_queue.put(frame.copy())
        
        # Pegar resultado se disponível
        if not self.result_queue.empty():
            self.last_results = self.result_queue.get()
        
        return self.last_results
    
    def draw_results(self, frame: np.ndarray, results: List[Dict[str, Any]]) -> np.ndarray:
        """Desenhar resultados no frame"""
        frame_copy = frame.copy()
        
        for result in results:
            name = result['name']
            similarity = result['similarity']
            bbox = result['bbox']
            confidence = result.get('confidence', 1.0)
            
            x, y, w, h = bbox
            
            # Cor baseada no reconhecimento
            if name != "Desconhecido":
                color = (0, 255, 0)  # Verde para conhecidos
            else:
                color = (0, 0, 255)  # Vermelho para desconhecidos
            
            # Desenhar retângulo
            cv2.rectangle(frame_copy, (x, y), (x + w, y + h), color, 2)
            
            # Preparar texto
            if self.config.ui.show_confidence:
                label = f"{name} ({similarity:.2f})"
            else:
                label = name
            
            # Calcular posição do texto
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = self.config.ui.font_scale
            thickness = self.config.ui.font_thickness
            
            (text_width, text_height), baseline = cv2.getTextSize(
                label, font, font_scale, thickness
            )
            
            # Desenhar fundo do texto
            cv2.rectangle(
                frame_copy,
                (x, y - text_height - 10),
                (x + text_width, y),
                color,
                -1
            )
            
            # Desenhar texto
            cv2.putText(
                frame_copy,
                label,
                (x, y - 5),
                font,
                font_scale,
                (255, 255, 255),
                thickness
            )
        
        # Adicionar informações do sistema
        self._draw_system_info(frame_copy)
        
        return frame_copy
    
    def _draw_system_info(self, frame: np.ndarray):
        """Desenhar informações do sistema no frame"""
        y_offset = 30
        
        # FPS
        if self.config.ui.show_fps:
            fps_text = f"FPS: {self.current_fps:.1f}"
            cv2.putText(frame, fps_text, (10, y_offset),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            y_offset += 25
        
        # Pessoas conhecidas
        known_count = len(self.face_recognizer.known_embeddings)
        known_text = f"Pessoas conhecidas: {known_count}"
        cv2.putText(frame, known_text, (10, y_offset),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        y_offset += 20
        
        # Uso de memória
        memory_usage = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        memory_text = f"Memória: {memory_usage:.1f}MB"
        cv2.putText(frame, memory_text, (10, y_offset),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
        y_offset += 20
        
        # Tamanho da queue
        queue_size = self.frame_queue.qsize()
        queue_text = f"Queue: {queue_size}"
        cv2.putText(frame, queue_text, (10, y_offset),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    def update_fps(self):
        """Atualizar cálculo de FPS"""
        self.fps_counter += 1
        
        if self.fps_counter >= 30:  # Calcular FPS a cada 30 frames
            current_time = time.time()
            elapsed = current_time - self.fps_start_time
            self.current_fps = self.fps_counter / elapsed
            
            # Log de performance
            memory_usage = psutil.Process().memory_info().rss / 1024 / 1024
            logger.log_performance(self.current_fps, self.frame_queue.qsize(), memory_usage)
            
            # Reset
            self.fps_counter = 0
            self.fps_start_time = current_time
    
    def start_camera_recognition(self, camera_index: int = 0):
        """Iniciar reconhecimento com câmera"""
        # Inicializar câmera
        cap = cv2.VideoCapture(camera_index)
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, self.config.camera.width)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, self.config.camera.height)
        cap.set(cv2.CAP_PROP_FPS, self.config.camera.fps)
        
        if not cap.isOpened():
            logger.error("Erro ao abrir a câmera")
            return
        
        logger.info("Câmera inicializada")
        
        # Iniciar processamento
        self.start_processing_thread()
        
        print("📹 Controles:")
        print("   'q' - Sair")
        print("   'r' - Recarregar embeddings")
        print("   's' - Salvar frame")
        print("   'c' - Mostrar estatísticas")
        print("   'h' - Limpar cache")
        
        try:
            while True:
                ret, frame = cap.read()
                if not ret:
                    logger.error("Erro ao capturar frame")
                    break
                
                # Flip horizontal para melhor UX
                frame = cv2.flip(frame, 1)
                
                self.frame_count += 1
                self.update_fps()
                
                # Processar frame (apenas a cada N frames)
                if self.frame_count % self.config.camera.frame_skip == 0:
                    results = self.process_frame(frame)
                else:
                    results = self.last_results
                
                # Desenhar resultados
                frame_with_results = self.draw_results(frame, results)
                
                # Exibir frame
                cv2.imshow(self.config.ui.window_title, frame_with_results)
                
                # Controles de teclado
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    logger.info("Encerrando sistema...")
                    break
                elif key == ord('r'):
                    logger.info("Recarregando embeddings...")
                    self.face_recognizer.reload_embeddings()
                elif key == ord('s'):
                    filename = f'capture_{int(time.time())}.jpg'
                    cv2.imwrite(filename, frame)
                    logger.info(f"Frame salvo: {filename}")
                elif key == ord('c'):
                    stats = self.face_recognizer.get_statistics()
                    print("\n📊 Estatísticas do Sistema:")
                    for key, value in stats.items():
                        print(f"   {key}: {value}")
                elif key == ord('h'):
                    self.face_recognizer.clear_cache()
                    logger.info("Cache limpo")
        
        except KeyboardInterrupt:
            logger.info("Interrompido pelo usuário")
        
        finally:
            # Cleanup
            self.stop_processing_thread()
            cap.release()
            cv2.destroyAllWindows()
            logger.info("Recursos liberados")
    
    def get_system_status(self) -> Dict[str, Any]:
        """Obter status do sistema"""
        return {
            'processing': self.processing,
            'fps': self.current_fps,
            'frame_count': self.frame_count,
            'queue_size': self.frame_queue.qsize(),
            'memory_usage_mb': psutil.Process().memory_info().rss / 1024 / 1024,
            'recognition_stats': self.face_recognizer.get_statistics()
        }

def main():
    """Função principal"""
    print("🚀 Sistema de Reconhecimento Facial V2.0")
    print("=" * 50)
    
    # Verificar se há faces conhecidas
    if not os.path.exists("known_faces") or len(os.listdir("known_faces")) == 0:
        print("⚠️ Nenhuma face conhecida encontrada!")
        print("📁 Adicione fotos na pasta 'known_faces'")
        print("   Nomeie os arquivos com o nome da pessoa (ex: joao.jpg)")
        return
    
    # Criar sistema
    system = FaceRecognitionSystemV2()
    
    # Verificar se há embeddings
    if len(system.face_recognizer.known_embeddings) == 0:
        print("❌ Falha ao carregar ou criar embeddings")
        return
    
    # Iniciar reconhecimento
    system.start_camera_recognition(camera_index=0)

if __name__ == "__main__":
    main()
