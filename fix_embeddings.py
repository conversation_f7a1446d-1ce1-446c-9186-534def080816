"""
Script para corrigir incompatibilidade de embeddings
"""
import os
import sys
import shutil
from datetime import datetime

# Adicionar o diretório atual ao path
sys.path.append('.')

from config import SystemConfig, DEFAULT_CONFIG
from face_recognizer import FaceRecognizer
from logger import logger

def backup_old_embeddings():
    """Fazer backup dos embeddings antigos"""
    if os.path.exists("face_embeddings.pkl"):
        backup_name = f"face_embeddings_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pkl"
        shutil.copy2("face_embeddings.pkl", backup_name)
        print(f"✅ Backup criado: {backup_name}")
        return True
    return False

def fix_embeddings():
    """Corrigir embeddings incompatíveis"""
    print("🔧 Corrigindo incompatibilidade de embeddings...")
    print("=" * 50)
    
    # Fazer backup
    backup_old_embeddings()
    
    # Remover embeddings antigos
    if os.path.exists("face_embeddings.pkl"):
        os.remove("face_embeddings.pkl")
        print("🗑️  Embeddings antigos removidos")
    
    # Verificar se há faces conhecidas
    if not os.path.exists("known_faces") or len(os.listdir("known_faces")) == 0:
        print("❌ Nenhuma face conhecida encontrada!")
        print("📁 Adicione fotos na pasta 'known_faces'")
        return False
    
    # Criar novo reconhecedor com modelo correto
    print("🔄 Criando novos embeddings com modelo Facenet...")
    
    recognizer = FaceRecognizer(
        known_faces_dir="known_faces",
        embeddings_file="face_embeddings.pkl",
        model_name="Facenet",  # Usar Facenet (128 dimensões)
        threshold=0.6
    )
    
    if len(recognizer.known_embeddings) > 0:
        print(f"✅ {len(recognizer.known_embeddings)} embeddings criados com sucesso!")
        print("🎯 Dimensões dos embeddings: 128 (Facenet)")
        return True
    else:
        print("❌ Falha ao criar embeddings")
        return False

def main():
    """Função principal"""
    print("🚀 Corretor de Embeddings - Sistema de Reconhecimento Facial")
    print("=" * 60)
    
    try:
        success = fix_embeddings()
        
        if success:
            print("\n✅ Correção concluída com sucesso!")
            print("🎉 Agora você pode executar o sistema normalmente:")
            print("   python face_recognition_system_v2.py")
        else:
            print("\n❌ Falha na correção dos embeddings")
            print("   Verifique se há fotos na pasta 'known_faces'")
    
    except Exception as e:
        print(f"\n❌ Erro durante a correção: {e}")
        logger.error(f"Erro na correção de embeddings: {e}")

if __name__ == "__main__":
    main()
