"""
Testes para o sistema de reconhecimento facial
"""
import unittest
import numpy as np
import os
import tempfile
import shutil
from unittest.mock import Mock, patch
import sys

# Adicionar o diretório atual ao path
sys.path.append('.')

from config import SystemConfig, DEFAULT_CONFIG
from face_detector import FaceDetector, FaceEmbeddingExtractor
from face_recognizer import <PERSON>Recognizer
from logger import FaceRecognitionLogger

class TestFaceDetector(unittest.TestCase):
    """Testes para o detector de faces"""
    
    def setUp(self):
        self.detector = FaceDetector(detector_backend="opencv")
        self.test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
    
    def test_detector_initialization(self):
        """Testar inicialização do detector"""
        self.assertIsNotNone(self.detector.face_cascade)
        self.assertEqual(self.detector.detector_backend, "opencv")
    
    def test_detect_faces_opencv(self):
        """Testar detecção com OpenCV"""
        results = self.detector.detect_faces_opencv(self.test_image)
        self.assertIsInstance(results, list)
    
    def test_extract_face_region(self):
        """Testar extração de região da face"""
        bbox = (100, 100, 200, 200)
        face_region = self.detector.extract_face_region(self.test_image, bbox)
        self.assertEqual(face_region.shape, (200, 200, 3))
    
    def test_resize_face(self):
        """Testar redimensionamento da face"""
        face = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        resized = self.detector.resize_face(face, (160, 160))
        self.assertEqual(resized.shape, (160, 160, 3))

class TestFaceRecognizer(unittest.TestCase):
    """Testes para o reconhecedor de faces"""
    
    def setUp(self):
        # Criar diretório temporário para testes
        self.temp_dir = tempfile.mkdtemp()
        self.embeddings_file = os.path.join(self.temp_dir, "test_embeddings.pkl")
        
        self.recognizer = FaceRecognizer(
            known_faces_dir=self.temp_dir,
            embeddings_file=self.embeddings_file,
            model_name="Facenet"
        )
    
    def tearDown(self):
        # Limpar diretório temporário
        shutil.rmtree(self.temp_dir)
    
    def test_recognizer_initialization(self):
        """Testar inicialização do reconhecedor"""
        self.assertIsNotNone(self.recognizer.embedding_extractor)
        self.assertEqual(self.recognizer.threshold, 0.6)
    
    def test_calculate_similarity(self):
        """Testar cálculo de similaridade"""
        embedding1 = np.random.rand(128)
        embedding2 = np.random.rand(128)
        
        similarity = self.recognizer.calculate_similarity(embedding1, embedding2)
        self.assertIsInstance(similarity, float)
        self.assertGreaterEqual(similarity, -1.0)
        self.assertLessEqual(similarity, 1.0)
    
    def test_recognize_face_no_known_faces(self):
        """Testar reconhecimento sem faces conhecidas"""
        test_embedding = np.random.rand(128)
        name, similarity, proc_time = self.recognizer.recognize_face(test_embedding)
        
        self.assertIsNone(name)
        self.assertEqual(similarity, 0.0)
        self.assertGreater(proc_time, 0.0)
    
    def test_add_person(self):
        """Testar adição de pessoa"""
        # Mock do extrator de embedding
        mock_embedding = np.random.rand(128)
        with patch.object(self.recognizer.embedding_extractor, 'extract_embedding', return_value=mock_embedding):
            result = self.recognizer.add_person("test_person", np.random.randint(0, 255, (160, 160, 3), dtype=np.uint8))
            self.assertTrue(result)
            self.assertIn("test_person", self.recognizer.known_embeddings)
    
    def test_get_statistics(self):
        """Testar obtenção de estatísticas"""
        stats = self.recognizer.get_statistics()
        self.assertIn('total_recognitions', stats)
        self.assertIn('successful_recognitions', stats)
        self.assertIn('known_persons', stats)

class TestSystemConfig(unittest.TestCase):
    """Testes para configuração do sistema"""
    
    def test_default_config(self):
        """Testar configuração padrão"""
        config = DEFAULT_CONFIG
        self.assertIsNotNone(config.model)
        self.assertIsNotNone(config.camera)
        self.assertIsNotNone(config.processing)
        self.assertIsNotNone(config.ui)
    
    def test_config_to_dict(self):
        """Testar conversão para dicionário"""
        config = DEFAULT_CONFIG
        config_dict = config.to_dict()
        self.assertIsInstance(config_dict, dict)
        self.assertIn('model', config_dict)
        self.assertIn('camera', config_dict)

class TestLogger(unittest.TestCase):
    """Testes para o sistema de logging"""
    
    def setUp(self):
        self.temp_log = tempfile.NamedTemporaryFile(delete=False, suffix='.log')
        self.temp_log.close()
        self.logger = FaceRecognitionLogger(self.temp_log.name)
    
    def tearDown(self):
        os.unlink(self.temp_log.name)
    
    def test_logger_initialization(self):
        """Testar inicialização do logger"""
        self.assertIsNotNone(self.logger.logger)
    
    def test_log_methods(self):
        """Testar métodos de log"""
        # Estes métodos não devem gerar exceções
        self.logger.info("Test info message")
        self.logger.warning("Test warning message")
        self.logger.error("Test error message")
        self.logger.debug("Test debug message")
    
    def test_log_recognition(self):
        """Testar log específico de reconhecimento"""
        self.logger.log_recognition("test_person", 0.85, 0.1)
        # Verificar se o arquivo foi criado e tem conteúdo
        self.assertTrue(os.path.exists(self.temp_log.name))
        with open(self.temp_log.name, 'r') as f:
            content = f.read()
            self.assertIn("test_person", content)

class TestIntegration(unittest.TestCase):
    """Testes de integração"""
    
    def setUp(self):
        self.temp_dir = tempfile.mkdtemp()
        self.embeddings_file = os.path.join(self.temp_dir, "test_embeddings.pkl")
    
    def tearDown(self):
        shutil.rmtree(self.temp_dir)
    
    def test_face_detection_and_recognition_flow(self):
        """Testar fluxo completo de detecção e reconhecimento"""
        # Criar detector e reconhecedor
        detector = FaceDetector()
        recognizer = FaceRecognizer(
            known_faces_dir=self.temp_dir,
            embeddings_file=self.embeddings_file
        )
        
        # Criar imagem de teste
        test_image = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        
        # Detectar faces
        faces = detector.detect_faces(test_image)
        self.assertIsInstance(faces, list)
        
        # Adicionar pessoa de teste (mock)
        mock_embedding = np.random.rand(128)
        with patch.object(recognizer.embedding_extractor, 'extract_embedding', return_value=mock_embedding):
            recognizer.add_person("test_person", test_image)
            
            # Reconhecer face
            name, similarity, proc_time = recognizer.recognize_face(mock_embedding)
            self.assertIsNotNone(name)
            self.assertGreaterEqual(similarity, 0.0)

def run_performance_test():
    """Teste de performance básico"""
    print("\n🧪 Executando teste de performance...")
    
    # Criar sistema
    from face_recognition_system_v2 import FaceRecognitionSystemV2
    
    config = DEFAULT_CONFIG
    config.camera.width = 320  # Resolução menor para teste
    config.camera.height = 240
    
    system = FaceRecognitionSystemV2(config)
    
    # Testar com imagem dummy
    test_frame = np.random.randint(0, 255, (240, 320, 3), dtype=np.uint8)
    
    import time
    start_time = time.time()
    
    # Processar múltiplos frames
    for i in range(10):
        results = system.process_frame(test_frame)
    
    end_time = time.time()
    processing_time = end_time - start_time
    
    print(f"✅ Processamento de 10 frames em {processing_time:.2f}s")
    print(f"📊 FPS médio: {10/processing_time:.1f}")
    
    # Verificar estatísticas
    stats = system.get_system_status()
    print(f"📈 Estatísticas: {stats}")

if __name__ == "__main__":
    print("🧪 Executando testes do sistema de reconhecimento facial...")
    
    # Executar testes unitários
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    # Executar teste de performance
    try:
        run_performance_test()
    except Exception as e:
        print(f"❌ Erro no teste de performance: {e}")
    
    print("\n✅ Testes concluídos!")
