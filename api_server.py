"""
API REST para o sistema de reconhecimento facial
"""
from flask import Flask, request, jsonify, send_file
from flask_cors import CORS
import cv2
import numpy as np
import base64
import io
import os
import json
from datetime import datetime
from typing import Dict, Any, List
import threading
import time

from config import SystemConfig, DEFAULT_CONFIG
from face_recognition_system_v2 import FaceRecognitionSystemV2
from advanced_features import (
    EmotionDetector, AgeGenderDetector, AttendanceTracker,
    NotificationSystem, AdvancedAnalytics
)
from logger import logger

class FaceRecognitionAPI:
    """API REST para o sistema de reconhecimento facial"""
    
    def __init__(self, config: SystemConfig = None):
        self.app = Flask(__name__)
        CORS(self.app)  # Permitir CORS para integração web
        
        self.config = config or DEFAULT_CONFIG
        self.system = None
        self.is_processing = False
        
        # Componentes avançados
        self.emotion_detector = EmotionDetector()
        self.age_gender_detector = AgeGenderDetector()
        self.attendance_tracker = AttendanceTracker()
        self.notification_system = NotificationSystem()
        self.analytics = AdvancedAnalytics()
        
        # Cache de reconhecimentos recentes
        self.recent_recognitions = []
        self.max_cache_size = 100
        
        # Inicializar sistema
        self.initialize_system()
        self.setup_routes()
    
    def initialize_system(self):
        """Inicializar sistema de reconhecimento"""
        try:
            self.system = FaceRecognitionSystemV2(self.config)
            logger.info("Sistema de reconhecimento inicializado para API")
        except Exception as e:
            logger.error(f"Erro ao inicializar sistema: {e}")
            raise
    
    def setup_routes(self):
        """Configurar rotas da API"""
        
        @self.app.route('/api/health', methods=['GET'])
        def health_check():
            """Verificar saúde da API"""
            return jsonify({
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'version': '2.0.0'
            })
        
        @self.app.route('/api/recognize', methods=['POST'])
        def recognize_face():
            """Reconhecer face em imagem"""
            try:
                data = request.get_json()
                
                if 'image' not in data:
                    return jsonify({'error': 'Imagem não fornecida'}), 400
                
                # Decodificar imagem base64
                image_data = data['image']
                if image_data.startswith('data:image'):
                    image_data = image_data.split(',')[1]
                
                image_bytes = base64.b64decode(image_data)
                nparr = np.frombuffer(image_bytes, np.uint8)
                image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                
                if image is None:
                    return jsonify({'error': 'Imagem inválida'}), 400
                
                # Processar reconhecimento
                results = self.process_image(image)
                
                return jsonify({
                    'success': True,
                    'results': results,
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                logger.error(f"Erro no reconhecimento: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/recognize/file', methods=['POST'])
        def recognize_face_file():
            """Reconhecer face em arquivo de imagem"""
            try:
                if 'file' not in request.files:
                    return jsonify({'error': 'Arquivo não fornecido'}), 400
                
                file = request.files['file']
                if file.filename == '':
                    return jsonify({'error': 'Nenhum arquivo selecionado'}), 400
                
                # Ler imagem
                file_bytes = file.read()
                nparr = np.frombuffer(file_bytes, np.uint8)
                image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                
                if image is None:
                    return jsonify({'error': 'Formato de imagem inválido'}), 400
                
                # Processar reconhecimento
                results = self.process_image(image)
                
                return jsonify({
                    'success': True,
                    'results': results,
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                logger.error(f"Erro no reconhecimento de arquivo: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/attendance', methods=['GET'])
        def get_attendance():
            """Obter dados de presença"""
            try:
                date = request.args.get('date')
                if date:
                    # Filtrar por data específica
                    attendance_data = self.attendance_tracker.attendance_data
                    records = [r for r in attendance_data['records'] if r['date'] == date]
                else:
                    # Presenças de hoje
                    records = self.attendance_tracker.get_today_attendance()
                
                return jsonify({
                    'success': True,
                    'attendance': records,
                    'count': len(records),
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                logger.error(f"Erro ao obter presença: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/attendance/<person_name>', methods=['GET'])
        def get_person_attendance(person_name):
            """Obter presença de uma pessoa específica"""
            try:
                stats = self.attendance_tracker.get_person_statistics(person_name)
                return jsonify({
                    'success': True,
                    'person': person_name,
                    'statistics': stats,
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                logger.error(f"Erro ao obter presença da pessoa: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/notifications', methods=['GET'])
        def get_notifications():
            """Obter notificações recentes"""
            try:
                limit = request.args.get('limit', 20, type=int)
                notifications = self.notification_system.get_recent_notifications(limit)
                
                return jsonify({
                    'success': True,
                    'notifications': notifications,
                    'count': len(notifications),
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                logger.error(f"Erro ao obter notificações: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/analytics', methods=['GET'])
        def get_analytics():
            """Obter dados de analytics"""
            try:
                report_type = request.args.get('type', 'daily')
                
                if report_type == 'daily':
                    date = request.args.get('date')
                    data = self.analytics.get_daily_report(date)
                elif report_type == 'hourly':
                    data = self.analytics.get_hourly_heatmap()
                elif report_type == 'emotions':
                    data = self.analytics.get_emotion_distribution()
                else:
                    return jsonify({'error': 'Tipo de relatório inválido'}), 400
                
                return jsonify({
                    'success': True,
                    'type': report_type,
                    'data': data,
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                logger.error(f"Erro ao obter analytics: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/analytics/export', methods=['GET'])
        def export_analytics():
            """Exportar analytics"""
            try:
                filename = self.analytics.export_analytics()
                return send_file(filename, as_attachment=True)
                
            except Exception as e:
                logger.error(f"Erro ao exportar analytics: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/people', methods=['GET'])
        def get_known_people():
            """Obter lista de pessoas conhecidas"""
            try:
                people = list(self.system.face_recognizer.known_embeddings.keys())
                return jsonify({
                    'success': True,
                    'people': people,
                    'count': len(people),
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                logger.error(f"Erro ao obter pessoas conhecidas: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/people', methods=['POST'])
        def add_person():
            """Adicionar nova pessoa"""
            try:
                data = request.get_json()
                
                if 'name' not in data or 'image' not in data:
                    return jsonify({'error': 'Nome e imagem são obrigatórios'}), 400
                
                name = data['name']
                image_data = data['image']
                
                # Decodificar imagem
                if image_data.startswith('data:image'):
                    image_data = image_data.split(',')[1]
                
                image_bytes = base64.b64decode(image_data)
                nparr = np.frombuffer(image_bytes, np.uint8)
                image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
                
                if image is None:
                    return jsonify({'error': 'Imagem inválida'}), 400
                
                # Adicionar pessoa
                success = self.system.face_recognizer.add_person(name, image)
                
                if success:
                    self.notification_system.add_notification('info', f'Pessoa {name} adicionada')
                    return jsonify({
                        'success': True,
                        'message': f'Pessoa {name} adicionada com sucesso',
                        'timestamp': datetime.now().isoformat()
                    })
                else:
                    return jsonify({'error': 'Falha ao adicionar pessoa'}), 500
                
            except Exception as e:
                logger.error(f"Erro ao adicionar pessoa: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/people/<person_name>', methods=['DELETE'])
        def remove_person(person_name):
            """Remover pessoa"""
            try:
                success = self.system.face_recognizer.remove_person(person_name)
                
                if success:
                    self.notification_system.add_notification('info', f'Pessoa {person_name} removida')
                    return jsonify({
                        'success': True,
                        'message': f'Pessoa {person_name} removida com sucesso',
                        'timestamp': datetime.now().isoformat()
                    })
                else:
                    return jsonify({'error': 'Pessoa não encontrada'}), 404
                
            except Exception as e:
                logger.error(f"Erro ao remover pessoa: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/status', methods=['GET'])
        def get_system_status():
            """Obter status do sistema"""
            try:
                if self.system:
                    status = self.system.get_system_status()
                else:
                    status = {'error': 'Sistema não inicializado'}
                
                return jsonify({
                    'success': True,
                    'status': status,
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                logger.error(f"Erro ao obter status: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/config', methods=['GET'])
        def get_config():
            """Obter configuração atual"""
            try:
                config_dict = self.config.to_dict()
                return jsonify({
                    'success': True,
                    'config': config_dict,
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                logger.error(f"Erro ao obter configuração: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/config', methods=['POST'])
        def update_config():
            """Atualizar configuração"""
            try:
                data = request.get_json()
                
                # Atualizar configurações
                if 'model' in data:
                    self.config.model.name = data['model'].get('name', self.config.model.name)
                    self.config.model.threshold = data['model'].get('threshold', self.config.model.threshold)
                    self.config.model.detector_backend = data['model'].get('detector_backend', self.config.model.detector_backend)
                
                # Reinicializar sistema
                self.initialize_system()
                
                return jsonify({
                    'success': True,
                    'message': 'Configuração atualizada com sucesso',
                    'timestamp': datetime.now().isoformat()
                })
                
            except Exception as e:
                logger.error(f"Erro ao atualizar configuração: {e}")
                return jsonify({'error': str(e)}), 500
    
    def process_image(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """Processar imagem para reconhecimento"""
        try:
            # Detectar faces
            detected_faces = self.system.face_detector.detect_faces(image)
            
            if not detected_faces:
                return []
            
            results = []
            
            for face_data in detected_faces:
                bbox = face_data['bbox']
                x, y, w, h = bbox
                face_region = image[y:y+h, x:x+w]
                
                # Extrair embedding
                embedding = self.system.face_recognizer.embedding_extractor.extract_embedding(face_region)
                
                if embedding is not None:
                    # Reconhecer face
                    name, similarity, proc_time = self.system.face_recognizer.recognize_face(embedding)
                    
                    # Detectar emoção e idade/gênero
                    emotion_data = self.emotion_detector.detect_emotion(face_region)
                    age_gender_data = self.age_gender_detector.detect_age_gender(face_region)
                    
                    # Registrar presença se reconhecido
                    if name != "Desconhecido":
                        self.attendance_tracker.record_presence(name, similarity)
                        self.analytics.record_recognition(name, similarity, 
                                                        emotion_data['emotion'],
                                                        age_gender_data['age'],
                                                        age_gender_data['gender'])
                    
                    result = {
                        'name': name,
                        'similarity': float(similarity),
                        'bbox': bbox,
                        'confidence': face_data.get('confidence', 1.0),
                        'processing_time': float(proc_time),
                        'emotion': emotion_data,
                        'age_gender': age_gender_data
                    }
                    
                    results.append(result)
                    
                    # Adicionar ao cache
                    self.add_to_cache(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Erro no processamento de imagem: {e}")
            return []
    
    def add_to_cache(self, result: Dict[str, Any]):
        """Adicionar resultado ao cache"""
        self.recent_recognitions.append({
            'timestamp': datetime.now().isoformat(),
            'result': result
        })
        
        # Manter apenas os últimos N resultados
        if len(self.recent_recognitions) > self.max_cache_size:
            self.recent_recognitions = self.recent_recognitions[-self.max_cache_size:]
    
    def run(self, host='0.0.0.0', port=5000, debug=False):
        """Executar servidor API"""
        logger.info(f"Iniciando servidor API em {host}:{port}")
        self.app.run(host=host, port=port, debug=debug)

def main():
    """Função principal da API"""
    try:
        # Configurar API
        api = FaceRecognitionAPI()
        
        # Executar servidor
        api.run(host='0.0.0.0', port=5000, debug=False)
        
    except Exception as e:
        logger.error(f"Erro na API: {e}")
        print(f"Erro fatal na API: {e}")

if __name__ == "__main__":
    main()
