"""
Demonstração completa do sistema de reconhecimento facial v2.0
"""
import os
import sys
import time
import cv2
import numpy as np
from datetime import datetime
import argparse
from typing import List, Dict, Any

# Adicionar o diretório atual ao path
sys.path.append('.')

from config import SystemConfig, DEFAULT_CONFIG
from face_recognition_system_v2 import FaceRecognitionSystemV2
from advanced_features import (
    EmotionDetector, AgeGenderDetector, AttendanceTracker,
    NotificationSystem, VideoRecorder, AdvancedAnalytics
)
from logger import logger

class CompleteDemo:
    """Demonstração completa do sistema"""
    
    def __init__(self, config: SystemConfig = None):
        self.config = config or DEFAULT_CONFIG
        self.system = None
        
        # Componentes avançados
        self.emotion_detector = EmotionDetector()
        self.age_gender_detector = AgeGenderDetector()
        self.attendance_tracker = AttendanceTracker()
        self.notification_system = NotificationSystem()
        self.video_recorder = VideoRecorder()
        self.analytics = AdvancedAnalytics()
        
        # Controle de demonstração
        self.demo_running = False
        self.demo_start_time = None
        self.recognition_count = 0
        
    def initialize_system(self):
        """Inicializar sistema"""
        print("🚀 Inicializando Sistema de Reconhecimento Facial v2.0")
        print("=" * 60)
        
        try:
            self.system = FaceRecognitionSystemV2(self.config)
            print("✅ Sistema inicializado com sucesso")
            
            # Verificar se há pessoas conhecidas
            if len(self.system.face_recognizer.known_embeddings) == 0:
                print("⚠️  Nenhuma pessoa conhecida encontrada")
                print("   Adicione fotos na pasta 'known_faces' para testar o reconhecimento")
                return False
            
            print(f"👥 {len(self.system.face_recognizer.known_embeddings)} pessoas conhecidas carregadas")
            return True
            
        except Exception as e:
            print(f"❌ Erro ao inicializar sistema: {e}")
            return False
    
    def run_demo(self, duration: int = 60):
        """Executar demonstração"""
        if not self.initialize_system():
            return
        
        print(f"\n🎬 Iniciando demonstração de {duration} segundos...")
        print("📹 Pressione 'q' para sair a qualquer momento")
        print("=" * 60)
        
        # Inicializar câmera
        cap = cv2.VideoCapture(0)
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        
        if not cap.isOpened():
            print("❌ Erro ao abrir câmera")
            return
        
        # Iniciar processamento
        self.system.start_processing_thread()
        self.demo_running = True
        self.demo_start_time = time.time()
        
        try:
            while self.demo_running:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Flip horizontal
                frame = cv2.flip(frame, 1)
                
                # Processar frame
                results = self.system.process_frame(frame)
                
                # Processar resultados
                self.process_demo_results(frame, results)
                
                # Desenhar informações da demonstração
                self.draw_demo_info(frame)
                
                # Exibir frame
                cv2.imshow('Demonstração - Sistema de Reconhecimento Facial v2.0', frame)
                
                # Verificar teclas
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q'):
                    break
                
                # Verificar duração da demonstração
                if time.time() - self.demo_start_time > duration:
                    print(f"\n⏰ Demonstração concluída após {duration} segundos")
                    break
        
        except KeyboardInterrupt:
            print("\n🛑 Demonstração interrompida pelo usuário")
        
        finally:
            # Cleanup
            self.demo_running = False
            self.system.stop_processing_thread()
            cap.release()
            cv2.destroyAllWindows()
            
            # Mostrar estatísticas finais
            self.show_final_stats()
    
    def process_demo_results(self, frame: np.ndarray, results: List[dict]):
        """Processar resultados da demonstração"""
        for result in results:
            person_name = result['name']
            similarity = result['similarity']
            bbox = result['bbox']
            
            x, y, w, h = bbox
            
            # Detectar emoção e idade/gênero
            face_region = frame[y:y+h, x:x+w]
            emotion_data = self.emotion_detector.detect_emotion(face_region)
            age_gender_data = self.age_gender_detector.detect_age_gender(face_region)
            
            # Registrar presença se reconhecido
            if person_name != "Desconhecido":
                self.attendance_tracker.record_presence(person_name, similarity)
                self.analytics.record_recognition(
                    person_name, similarity,
                    emotion_data['emotion'],
                    age_gender_data['age'],
                    age_gender_data['gender']
                )
                
                self.recognition_count += 1
                
                # Adicionar notificação
                self.notification_system.add_notification(
                    'recognition',
                    f"{person_name} reconhecido (confiança: {similarity:.2f})",
                    person_name
                )
                
                # Iniciar gravação se for um evento importante
                if similarity > 0.8:
                    self.video_recorder.start_recording(frame, "high_confidence", person_name)
            
            # Desenhar informações avançadas
            self.draw_advanced_info(frame, result, emotion_data, age_gender_data)
    
    def draw_advanced_info(self, frame: np.ndarray, result: dict, 
                          emotion_data: dict, age_gender_data: dict):
        """Desenhar informações avançadas no frame"""
        person_name = result['name']
        similarity = result['similarity']
        bbox = result['bbox']
        
        x, y, w, h = bbox
        
        # Cor baseada no reconhecimento
        if person_name != "Desconhecido":
            color = (0, 255, 0)  # Verde
        else:
            color = (0, 0, 255)  # Vermelho
        
        # Desenhar retângulo
        cv2.rectangle(frame, (x, y), (x + w, y + h), color, 2)
        
        # Informações básicas
        info_lines = [
            f"Nome: {person_name}",
            f"Confiança: {similarity:.2f}",
            f"Emoção: {emotion_data['emotion']} ({emotion_data['confidence']:.2f})",
            f"Idade: {age_gender_data['age']} anos",
            f"Gênero: {age_gender_data['gender']}"
        ]
        
        # Desenhar fundo para texto
        text_height = len(info_lines) * 20 + 10
        cv2.rectangle(frame, (x, y - text_height), (x + 200, y), color, -1)
        
        # Desenhar texto
        for i, line in enumerate(info_lines):
            cv2.putText(frame, line, (x + 5, y - text_height + 20 + i * 20),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)
    
    def draw_demo_info(self, frame: np.ndarray):
        """Desenhar informações da demonstração"""
        height, width = frame.shape[:2]
        
        # Informações do sistema
        info_lines = [
            "Sistema de Reconhecimento Facial v2.0",
            f"Tempo: {int(time.time() - self.demo_start_time)}s" if self.demo_start_time else "0s",
            f"Reconhecimentos: {self.recognition_count}",
            f"Presenças hoje: {len(self.attendance_tracker.get_today_attendance())}",
            f"Notificações: {len(self.notification_system.notifications)}"
        ]
        
        # Desenhar fundo
        cv2.rectangle(frame, (10, 10), (400, 120), (0, 0, 0), -1)
        cv2.rectangle(frame, (10, 10), (400, 120), (255, 255, 255), 2)
        
        # Desenhar texto
        for i, line in enumerate(info_lines):
            color = (0, 255, 255) if i == 0 else (255, 255, 255)
            font_scale = 0.7 if i == 0 else 0.5
            thickness = 2 if i == 0 else 1
            
            cv2.putText(frame, line, (20, 35 + i * 20),
                       cv2.FONT_HERSHEY_SIMPLEX, font_scale, color, thickness)
        
        # Adicionar frame à gravação se estiver gravando
        if self.video_recorder.current_recording is not None:
            self.video_recorder.add_frame(frame)
    
    def show_final_stats(self):
        """Mostrar estatísticas finais"""
        print("\n" + "=" * 60)
        print("📊 ESTATÍSTICAS FINAIS DA DEMONSTRAÇÃO")
        print("=" * 60)
        
        # Estatísticas do sistema
        if self.system:
            system_stats = self.system.get_system_status()
            print(f"🎯 Total de reconhecimentos: {system_stats['recognition_stats']['total_recognitions']}")
            print(f"✅ Reconhecimentos bem-sucedidos: {system_stats['recognition_stats']['successful_recognitions']}")
            print(f"❓ Faces desconhecidas: {system_stats['recognition_stats']['unknown_faces']}")
            print(f"📈 Taxa de sucesso: {system_stats['recognition_stats']['success_rate']:.1f}%")
            print(f"⏱️  Tempo médio de processamento: {system_stats['recognition_stats']['avg_processing_time']:.3f}s")
        
        # Estatísticas de presença
        today_attendance = self.attendance_tracker.get_today_attendance()
        print(f"\n👥 Presenças registradas hoje: {len(today_attendance)}")
        
        if today_attendance:
            print("   Últimas presenças:")
            for record in today_attendance[-5:]:  # Últimas 5
                timestamp = datetime.fromisoformat(record['timestamp']).strftime("%H:%M:%S")
                print(f"   - {record['person']} às {timestamp} (confiança: {record['confidence']:.2f})")
        
        # Estatísticas de analytics
        daily_report = self.analytics.get_daily_report()
        print(f"\n📊 Relatório diário:")
        print(f"   - Data: {daily_report['date']}")
        print(f"   - Total de reconhecimentos: {daily_report['total_recognitions']}")
        print(f"   - Pessoas únicas: {daily_report['unique_people']}")
        print(f"   - Confiança média: {daily_report['average_confidence']:.3f}")
        
        # Distribuição de emoções
        emotion_dist = self.analytics.get_emotion_distribution()
        if emotion_dist:
            print(f"\n😊 Distribuição de emoções:")
            for emotion, count in sorted(emotion_dist.items(), key=lambda x: x[1], reverse=True):
                print(f"   - {emotion}: {count}")
        
        # Notificações recentes
        recent_notifications = self.notification_system.get_recent_notifications(5)
        if recent_notifications:
            print(f"\n🔔 Últimas notificações:")
            for notification in recent_notifications:
                timestamp = datetime.fromisoformat(notification['timestamp']).strftime("%H:%M:%S")
                print(f"   - [{timestamp}] {notification['icon']} {notification['message']}")
        
        print("\n✅ Demonstração concluída!")
        print("=" * 60)

def main():
    """Função principal"""
    parser = argparse.ArgumentParser(description='Demonstração do Sistema de Reconhecimento Facial v2.0')
    parser.add_argument('--duration', type=int, default=60, help='Duração da demonstração em segundos')
    parser.add_argument('--config', type=str, help='Arquivo de configuração personalizado')
    
    args = parser.parse_args()
    
    # Carregar configuração
    config = DEFAULT_CONFIG
    if args.config and os.path.exists(args.config):
        try:
            # Implementar carregamento de configuração personalizada
            print(f"📁 Carregando configuração de {args.config}")
        except Exception as e:
            print(f"⚠️  Erro ao carregar configuração: {e}")
            print("   Usando configuração padrão")
    
    # Executar demonstração
    demo = CompleteDemo(config)
    demo.run_demo(args.duration)

if __name__ == "__main__":
    main()
