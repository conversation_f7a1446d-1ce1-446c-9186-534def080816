"""
Configuração para Sistema de Múltiplas Câmeras
"""
from dataclasses import dataclass
from typing import List, Dict, Any

@dataclass
class CameraConfig:
    """Configuração de uma câmera"""
    id: str
    type: str  # 'local' ou 'rtsp'
    path: str
    width: int = 640
    height: int = 480
    enabled: bool = True
    description: str = ""

# Configurações pré-definidas de câmeras
CAMERA_CONFIGS = {
    # Câmeras locais
    'local_0': CameraConfig(
        id='local_0',
        type='local',
        path='0',
        width=640,
        height=480,
        description='Câmera local (índice 0)'
    ),
    'local_1': CameraConfig(
        id='local_1',
        type='local',
        path='1',
        width=640,
        height=480,
        description='Câmera local (índice 1)'
    ),
    
    # Exemplos de RTSP (substitua pelas suas URLs)
    'rtsp_1': CameraConfig(
        id='rtsp_1',
        type='rtsp',
        path='rtsp://admin:password@*************:554/stream1',
        width=640,
        height=480,
        description='Câmera IP 1'
    ),
    'rtsp_2': CameraConfig(
        id='rtsp_2',
        type='rtsp',
        path='rtsp://admin:password@*************:554/stream1',
        width=640,
        height=480,
        description='Câmera IP 2'
    ),
    
    # Exemplo de arquivo de vídeo
    'video_file': CameraConfig(
        id='video_file',
        type='local',
        path='test_video.mp4',
        width=640,
        height=480,
        description='Arquivo de vídeo de teste'
    )
}

# Configurações de layout
@dataclass
class DisplayConfig:
    """Configuração de exibição"""
    width: int = 1280
    height: int = 480
    show_fps: bool = True
    show_stats: bool = True
    show_camera_info: bool = True
    border_color: tuple = (0, 255, 0)
    border_thickness: int = 2

# Configurações de processamento
@dataclass
class ProcessingConfig:
    """Configuração de processamento"""
    max_fps: int = 30
    skip_frames: int = 2  # Processar 1 a cada skip_frames
    confidence_threshold: float = 0.6
    enable_emotion: bool = True
    enable_age_gender: bool = True
    enable_recording: bool = True

# Configurações de rede RTSP
@dataclass
class RTSPConfig:
    """Configuração RTSP"""
    timeout: int = 5000  # ms
    buffer_size: int = 1024
    reconnect_attempts: int = 3
    reconnect_delay: int = 5  # segundos

def get_camera_configs(selected_cameras: List[str] = None) -> List[CameraConfig]:
    """Obter configurações de câmeras selecionadas"""
    if selected_cameras is None:
        # Retornar todas as câmeras habilitadas
        return [config for config in CAMERA_CONFIGS.values() if config.enabled]
    
    return [CAMERA_CONFIGS[cam_id] for cam_id in selected_cameras if cam_id in CAMERA_CONFIGS]

def create_camera_specs(camera_configs: List[CameraConfig]) -> List[str]:
    """Criar especificações de câmeras para o sistema"""
    specs = []
    for config in camera_configs:
        if config.enabled:
            spec = f"{config.id}:{config.type}:{config.path}"
            specs.append(spec)
    return specs

# Exemplos de uso
if __name__ == "__main__":
    print("Configurações de Câmeras Disponíveis:")
    print("=" * 50)
    
    for cam_id, config in CAMERA_CONFIGS.items():
        status = "✅ Habilitada" if config.enabled else "❌ Desabilitada"
        print(f"{cam_id}: {config.description} - {status}")
        print(f"  Tipo: {config.type}, Caminho: {config.path}")
        print(f"  Resolução: {config.width}x{config.height}")
        print()
    
    print("\nExemplos de uso:")
    print("=" * 30)
    
    # Exemplo 1: Apenas câmera local
    local_configs = get_camera_configs(['local_0'])
    local_specs = create_camera_specs(local_configs)
    print(f"1. Câmera local: {' '.join(local_specs)}")
    
    # Exemplo 2: Câmera local + RTSP
    mixed_configs = get_camera_configs(['local_0', 'rtsp_1'])
    mixed_specs = create_camera_specs(mixed_configs)
    print(f"2. Múltiplas câmeras: {' '.join(mixed_specs)}")
    
    # Exemplo 3: Todas as câmeras habilitadas
    all_configs = get_camera_configs()
    all_specs = create_camera_specs(all_configs)
    print(f"3. Todas as câmeras: {' '.join(all_specs)}")
