"""
Nova interface simplificada e robusta para o sistema Escola Segura.

Principais metas:
- Layout simples, confiável e responsivo (Panedwindow horizontal).
- Botões Iniciar/Parar/Capturar e menu completo.
- Exibição de vídeo estável com thread dedicada.
- Listas de Reconhecimentos, Presenças e Notificações.
- Configuração de câmeras e alternância de recursos (emoções/idade&gênero).

Obs.: Não remove a interface antiga. Execute este arquivo para usar a nova UI.
"""
from __future__ import annotations

import os
import json
import threading
import time
from datetime import datetime
from typing import Dict, Any, List, Optional, Set
import queue

import tkinter as tk
from tkinter import ttk, messagebox, filedialog

import cv2
from PIL import Image, ImageTk

from config import DEFAULT_CONFIG
from multi_camera_system import MultiCameraSystem
from face_recognizer import FaceRecognizer
from face_detector import FaceDetector


class EscolaSeguraApp:
    def __init__(self) -> None:
        self.root = tk.Tk()
        self.root.title("Escola Segura — Nova Interface")
        self.root.geometry("1400x900")
        try:
            # Escala confortável por padrão (125%)
            self.root.tk.call('tk', 'scaling', 1.25)
        except Exception:
            pass

        # Estado do app
        self.config = DEFAULT_CONFIG
        self.system: Optional[MultiCameraSystem] = None
        self.running = False
        self._display_image = None  # manter referência PIL/ImageTk
        self._ui_thread: Optional[threading.Thread] = None
        self._analysis_thread: Optional[threading.Thread] = None
        self._sash_ratio = 0.72
        self._setting_sash = False
        self._current_frame_bgr = None
        # Estride de análise por câmera (quanto maior, mais fluido o vídeo)
        self._cam_stride: int = 3
        self._cam_counter: Dict[str, int] = {}
        # Últimos resultados de análise por câmera (reutilizados entre análises)
        self._last_results: Dict[str, Dict[str, Any]] = {}
        # Fila e controle para análise assíncrona (não bloquear exibição)
        self._analysis_queue: "queue.Queue[tuple[str, Any]]" = queue.Queue(maxsize=8)
        self._analysis_inflight: Set[str] = set()
        self._last_analysis_ts: Dict[str, float] = {}
        self._analysis_min_interval: float = 0.12  # seg
        # Frame pendente e atualizações pendentes para aplicar na UI thread
        self._pending_pil: Optional[Image.Image] = None
        self._pending_stats: Optional[Dict[str, Any]] = None
        self._pending_notifications: Optional[List[Dict[str, Any]]] = None
        # Ticker de UI (30 FPS alvo)
        self._ui_interval_ms: int = 33
        # Resolução fixa de exibição (evita reflows/flicker por redimensionar a cada frame)
        self._display_w: int = 960
        self._display_h: int = 540

        # Dados UI
        self.recognition_events: List[Dict[str, Any]] = []

        # Construir UI
        self._build_menu()
        self._build_layout()
        self._bind_shortcuts()

        # Carregar config de câmeras
        self._load_camera_settings()

        # Ajuste tardio do sash
        self.root.after(600, self._set_sash_by_ratio)
        # Medir e ajustar resolução de exibição após layout estar pronto
        self.root.after(700, self._measure_display_size)
        # Iniciar ticker da UI
        self.root.after(self._ui_interval_ms, self._ui_tick)

    # ---------- UI ----------
    def _build_menu(self) -> None:
        menubar = tk.Menu(self.root)
        self.root.config(menu=menubar)

        # Arquivo
        m_file = tk.Menu(menubar, tearoff=0)
        m_file.add_command(label="Adicionar Pessoa...", command=self.add_person)
        m_file.add_command(label="Adicionar Pessoa (Upload)...", command=self.add_person_upload)
        m_file.add_command(label="Gerenciar Pessoas...", command=self.manage_people)
        m_file.add_separator()
        m_file.add_command(label="Recriar Embeddings...", command=self.rebuild_embeddings)
        m_file.add_separator()
        m_file.add_command(label="Configurar Câmeras...", command=self.open_camera_config)
        m_file.add_separator()
        m_file.add_command(label="Exportar Dados...", command=self.export_data)
        m_file.add_command(label="Importar Dados...", command=self.import_data)
        m_file.add_separator()
        m_file.add_command(label="Sair", command=self.root.quit)
        menubar.add_cascade(label="Arquivo", menu=m_file)

        # Visualização
        m_view = tk.Menu(menubar, tearoff=0)
        # Proporção do painel
        m_ratio = tk.Menu(m_view, tearoff=0)
        for label, r in (("50%/50%", 0.5), ("60%/40%", 0.6), ("65%/35%", 0.65), ("70%/30%", 0.7), ("75%/25%", 0.75)):
            m_ratio.add_command(label=label, command=lambda v=r: self.set_side_panel_ratio(v))
        m_view.add_cascade(label="Largura do Painel", menu=m_ratio)
        # Fluidez (stride de análise)
        m_perf = tk.Menu(m_view, tearoff=0)
        for label, s in (("Máxima (analisar 1/4)",4),("Alta (1/3)",3),("Equilíbrio (1/2)",2),("Detalhe (1/1)",1)):
            m_perf.add_command(label=label, command=lambda v=s: self.set_analysis_stride(v))
        m_view.add_cascade(label="Fluidez do Vídeo", menu=m_perf)
        m_view.add_separator()
        m_view.add_command(label="Tela Cheia (F11)", command=self.toggle_fullscreen)
        menubar.add_cascade(label="Visualização", menu=m_view)

        # Relatórios
        m_rep = tk.Menu(menubar, tearoff=0)
        m_rep.add_command(label="Relatório de Presença", command=self.attendance_report)
        m_rep.add_command(label="Exportar Analytics", command=self.export_analytics)
        menubar.add_cascade(label="Relatórios", menu=m_rep)

        # Ajuda
        m_help = tk.Menu(menubar, tearoff=0)
        m_help.add_command(label="Sobre", command=self.show_about)
        menubar.add_cascade(label="Ajuda", menu=m_help)

    def _build_layout(self) -> None:
        # Header
        header = ttk.Frame(self.root, padding=(10, 10))
        header.pack(fill='x')
        ttk.Label(header, text="Escola Segura — Reconhecimento Facial", font=('Segoe UI Semibold', 16)).pack(side='left')
        actions = ttk.Frame(header)
        actions.pack(side='right')
        self.btn_start = ttk.Button(actions, text="▶ Iniciar", command=self.start_all)
        self.btn_stop = ttk.Button(actions, text="■ Parar", command=self.stop_all, state='disabled')
        self.btn_cap = ttk.Button(actions, text="📷 Capturar", command=self.capture_frame)
        for b in (self.btn_start, self.btn_stop, self.btn_cap):
            b.pack(side='left', padx=5)

        # Main
        main = ttk.Frame(self.root)
        main.pack(fill='both', expand=True, padx=10, pady=10)

        self.paned = ttk.Panedwindow(main, orient=tk.HORIZONTAL)
        self.paned.pack(fill='both', expand=True)

        # Left (video)
        left = tk.Frame(self.paned, bg='black')
        # Usar tk.Label (não ttk) para vídeo, com fundo preto (evita 'piscadas' brancas)
        self.video_label = tk.Label(left, text="Bem-vindo!\nClique em Iniciar.", anchor='center', bg='black', fg='white', bd=0, highlightthickness=0)
        self.video_label.pack(fill='both', expand=True)
        # Controls under video
        ctrl = ttk.Frame(left)
        ctrl.pack(fill='x')
        self.var_emotion = tk.BooleanVar(value=True)
        self.var_age_gender = tk.BooleanVar(value=True)
        ttk.Checkbutton(ctrl, text='Emoções', variable=self.var_emotion, command=self.toggle_emotions).pack(side='left', padx=5)
        ttk.Checkbutton(ctrl, text='Idade/Gênero', variable=self.var_age_gender, command=self.toggle_age_gender).pack(side='left', padx=5)

        # Right (tabs)
        right = ttk.Frame(self.paned)
        self.tabs = ttk.Notebook(right)
        self.tabs.pack(fill='both', expand=True)

        # Tab Reconhecimento
        tab_rec = ttk.Frame(self.tabs)
        self.tabs.add(tab_rec, text='Reconhecimento')
        self.tree_rec = ttk.Treeview(tab_rec, columns=("Hora","Pessoa","Conf","Emoção","Idade","Gênero"), show='headings', height=12)
        for col, w in (("Hora", 80),("Pessoa",140),("Conf",70),("Emoção",120),("Idade",60),("Gênero",80)):
            self.tree_rec.heading(col, text=col)
            self.tree_rec.column(col, width=w, anchor='center')
        scr_rec = ttk.Scrollbar(tab_rec, orient='vertical', command=self.tree_rec.yview)
        self.tree_rec.configure(yscrollcommand=scr_rec.set)
        self.tree_rec.pack(side='left', fill='both', expand=True)
        scr_rec.pack(side='right', fill='y')

        # Tab Presença
        tab_att = ttk.Frame(self.tabs)
        self.tabs.add(tab_att, text='Presença')
        self.tree_att = ttk.Treeview(tab_att, columns=("Hora","Pessoa","Confiança"), show='headings', height=12)
        for col, w in (("Hora",90),("Pessoa",140),("Confiança",90)):
            self.tree_att.heading(col, text=col)
            self.tree_att.column(col, width=w, anchor='center')
        scr_att = ttk.Scrollbar(tab_att, orient='vertical', command=self.tree_att.yview)
        self.tree_att.configure(yscrollcommand=scr_att.set)
        self.tree_att.pack(side='left', fill='both', expand=True)
        scr_att.pack(side='right', fill='y')

        # Tab Notificações
        tab_not = ttk.Frame(self.tabs)
        self.tabs.add(tab_not, text='Notificações')
        self.list_not = tk.Listbox(tab_not, height=10)
        self.list_not.pack(fill='both', expand=True)

        # Tab Configurações
        tab_cfg = ttk.Frame(self.tabs)
        self.tabs.add(tab_cfg, text='Configurações')
        rowi = 0
        # Detector de faces
        ttk.Label(tab_cfg, text='Detector de Faces:').grid(row=rowi, column=0, sticky='w', padx=8, pady=6)
        self.detector_var = tk.StringVar(value=getattr(self.config.model, 'detector_backend', 'opencv'))
        det_frame = ttk.Frame(tab_cfg)
        det_frame.grid(row=rowi, column=1, sticky='w', padx=8, pady=6)
        ttk.Radiobutton(det_frame, text='Rápido (OpenCV)', value='opencv', variable=self.detector_var).pack(side='left', padx=5)
        ttk.Radiobutton(det_frame, text='Médio (ONNX)', value='onnx', variable=self.detector_var).pack(side='left', padx=5)
        ttk.Radiobutton(det_frame, text='Preciso (MTCNN/DeepFace)', value='mtcnn', variable=self.detector_var).pack(side='left', padx=5)
        rowi += 1

        # Modelo de Reconhecimento (Face Recognizer)
        ttk.Label(tab_cfg, text='Modelo de Reconhecimento:').grid(row=rowi, column=0, sticky='w', padx=8, pady=6)
        try:
            default_model = getattr(self.config.model, 'name', 'Facenet')
        except Exception:
            default_model = 'Facenet'
        self.recognizer_model_var = tk.StringVar(value=default_model)
        self.recognizer_model_combo = ttk.Combobox(
            tab_cfg,
            textvariable=self.recognizer_model_var,
            values=['Facenet', 'VGG-Face', 'OpenFace', 'DeepFace'],
            state='readonly'
        )
        self.recognizer_model_combo.grid(row=rowi, column=1, sticky='w', padx=8, pady=6)
        rowi += 1

        # Resolução de exibição
        ttk.Label(tab_cfg, text='Resolução de Exibição:').grid(row=rowi, column=0, sticky='w', padx=8, pady=6)
        self.display_res_var = tk.StringVar(value='Auto')
        res_values = ['Auto', '854x480', '1280x720', '1600x900']
        self.display_res_combo = ttk.Combobox(tab_cfg, textvariable=self.display_res_var, values=res_values, state='readonly')
        self.display_res_combo.grid(row=rowi, column=1, sticky='w', padx=8, pady=6)
        rowi += 1

        # Aplicar
        btns_cfg = ttk.Frame(tab_cfg)
        btns_cfg.grid(row=rowi, column=0, columnspan=2, pady=10, sticky='w')
        ttk.Button(btns_cfg, text='Aplicar Alterações', command=self.apply_settings).pack(side='left', padx=(0,8))
        ttk.Button(btns_cfg, text='Aplicar Perfil: Baixa Latência', command=self.apply_low_latency_profile).pack(side='left')
        # Grid config
        tab_cfg.columnconfigure(1, weight=1)

        # Status
        status = ttk.Frame(self.root, padding=(10, 5))
        status.pack(fill='x', side='bottom')
        self.lbl_fps = ttk.Label(status, text='FPS: 0.0')
        self.lbl_rec = ttk.Label(status, text='Reconhecimentos: 0')
        self.lbl_mem = ttk.Label(status, text='Memória: 0%')
        for w in (self.lbl_fps, self.lbl_rec, self.lbl_mem):
            w.pack(side='left', padx=12)

        self.paned.add(left, weight=3)
        self.paned.add(right, weight=1)
        self.paned.bind('<Configure>', self._on_paned_configure)

    def _bind_shortcuts(self) -> None:
        self.root.bind('<F11>', lambda e: self.toggle_fullscreen())
        self.root.bind('<Escape>', lambda e: self.exit_fullscreen())
        self.root.protocol("WM_DELETE_WINDOW", self.on_close)

    def restart_pipeline(self) -> None:
        try:
            self.stop_all()
        except Exception:
            pass
        self.root.after(150, self.start_all)

    def apply_low_latency_profile(self) -> None:
        """Aplicar perfil de baixa latência (vídeo fluido)."""
        try:
            # Ajustar detector, resolução e stride
            self.detector_var.set('opencv')
            self.display_res_var.set('854x480')
            self.det_width_var.set('320')
            # Mais fluidez na UI
            self._cam_stride = 4
            # Aplicar demais configs (threshold não alterado)
            self.apply_settings()
        except Exception:
            pass

    def apply_settings(self) -> None:
        """Aplicar detector e resolução de exibição. Reinicia o sistema se necessário."""
        needs_restart = False
        # Webcam device/backend
        try:
            dev = int(self.webcam_device_var.get())
            backend = self.webcam_backend_var.get().strip()
            changed = (self.camera_cfg.get('webcam_device', 0) != dev) or (self.camera_cfg.get('webcam_backend','CAP_DSHOW') != backend)
            if changed:
                self.camera_cfg['webcam_device'] = dev
                self.camera_cfg['webcam_backend'] = backend
                # Persistir camera_settings.json
                try:
                    with open('camera_settings.json', 'w', encoding='utf-8') as f:
                        json.dump(self.camera_cfg, f, indent=2, ensure_ascii=False)
                except Exception:
                    pass
                needs_restart = True
        except Exception:
            pass
        # Detector
        try:
            new_backend = self.detector_var.get().strip()
            if new_backend and new_backend != getattr(self.config.model, 'detector_backend', 'opencv'):
                self.config.model.detector_backend = new_backend
                needs_restart = True
        except Exception:
            pass
        # Modelo de Reconhecimento
        try:
            new_model = self.recognizer_model_var.get().strip()
            if new_model and new_model != getattr(self.config.model, 'name', 'Facenet'):
                self.config.model.name = new_model
                needs_restart = True
        except Exception:
            pass
        # Largura-alvo de detecção
        try:
            dw = int(self.det_width_var.get())
            if dw != getattr(self.config.processing, 'detection_width', 480):
                self.config.processing.detection_width = dw
        except Exception:
            pass
        # Threshold
        try:
            th = float(self.threshold_var.get())
            if th != getattr(self.config.model, 'threshold', 0.6):
                self.config.model.threshold = th
        except Exception:
            pass
        # OpenCV threads
        try:
            t = int(self.cv_threads_var.get())
            import cv2 as _cv
            try:
                _cv.setUseOptimized(True)
            except Exception:
                pass
            try:
                _cv.setNumThreads(int(t))
            except Exception:
                pass
        except Exception:
            pass
        # Resolução
        try:
            choice = self.display_res_var.get()
            if choice and choice.lower() != 'auto':
                if 'x' in choice:
                    w, h = choice.lower().split('x', 1)
                    self._display_w = int(w)
                    self._display_h = int(h)
            else:
                self._measure_display_size()
        except Exception:
            pass
        # Reiniciar se necessário
        if self.running and needs_restart:
            try:
                self.stop_all()
            except Exception:
                pass
            # pequeno delay para liberar recursos
            self.root.after(150, self.start_all)

    # ---------- Ações ----------
    def start_all(self) -> None:
        if self.running:
            return
        try:
            # Preferir GPU se disponível (TensorFlow/DeepFace)
            self._setup_gpu()
            self.system = MultiCameraSystem(self.config)
            # Adicionar câmeras
            if self.camera_cfg.get('webcam_enabled', True):
                # Seleção de backend via variáveis de ambiente (OpenCV prioridade)
                backend = str(self.camera_cfg.get('webcam_backend', 'CAP_DSHOW')).upper()
                if backend == 'CAP_MSMF':
                    os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '1000'
                    os.environ['OPENCV_VIDEOIO_PRIORITY_DSHOW'] = '0'
                elif backend == 'CAP_DSHOW':
                    os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '0'
                    os.environ['OPENCV_VIDEOIO_PRIORITY_DSHOW'] = '1000'
                else:
                    os.environ['OPENCV_VIDEOIO_PRIORITY_MSMF'] = '0'
                    os.environ['OPENCV_VIDEOIO_PRIORITY_DSHOW'] = '0'
                dev = str(self.camera_cfg.get('webcam_device', 0))
                self.system.add_camera('Webcam', 'local', dev, 640, 480)
            if self.camera_cfg.get('c100_enabled', False):
                url = self.camera_cfg.get('c100_url') or os.getenv('RTSP_C100_URL', '')
                if url:
                    self.system.add_camera('C100', 'rtsp', url, 640, 480)
            ok = self.system.initialize_system()
            if not ok:
                messagebox.showerror('Erro', 'Falha ao inicializar sistema')
                return
            if not self.system.start_processing():
                messagebox.showerror('Erro', 'Falha ao iniciar processamento')
                return
            # Aplicar toggles
            self.system.enable_emotion = bool(self.var_emotion.get())
            self.system.enable_age_gender = bool(self.var_age_gender.get())

            self.running = True
            self.btn_start.config(state='disabled')
            self.btn_stop.config(state='normal')

            # Thread de exibição
            self._ui_thread = threading.Thread(target=self._ui_loop, daemon=True)
            self._ui_thread.start()
            # Thread de análise (assíncrona)
            self._analysis_thread = threading.Thread(target=self._analysis_loop, daemon=True)
            self._analysis_thread.start()
        except Exception as e:
            messagebox.showerror('Erro', f'Falha ao iniciar: {e}')

    def stop_all(self) -> None:
        if not self.running:
            return
        try:
            self.running = False
            if self.system:
                self.system.stop()
                self.system = None
            self.btn_start.config(state='normal')
            self.btn_stop.config(state='disabled')
            # Placeholder
            self.video_label.config(image='', text="Bem-vindo!\nClique em Iniciar.")
            self._display_image = None
        except Exception:
            pass

    def capture_frame(self) -> None:
        if self._current_frame_bgr is None:
            return
        try:
            ts = datetime.now().strftime('%Y%m%d_%H%M%S')
            fn = f'capture_{ts}.jpg'
            cv2.imwrite(fn, self._current_frame_bgr)
            messagebox.showinfo('Captura', f'Frame salvo em {fn}')
        except Exception as e:
            messagebox.showerror('Erro', f'Falha ao salvar: {e}')

    def toggle_emotions(self) -> None:
        if self.system:
            self.system.enable_emotion = bool(self.var_emotion.get())

    def toggle_age_gender(self) -> None:
        if self.system:
            self.system.enable_age_gender = bool(self.var_age_gender.get())

    # ---------- Loop UI / Atualizações ----------
    def _ui_loop(self) -> None:
        try:
            while self.running and self.system is not None:
                all_results: List[Dict[str, Any]] = []
                updated_cameras: set[str] = set()
                # Recolher frames mais recentes de todas as câmeras (drena a fila)
                for cam in self.system.cameras:
                    cam_id = cam.source_id
                    q = self.system.frame_queues.get(cam_id)
                    if q is None or q.empty():
                        # reutiliza último resultado se existir (mantém layout)
                        if cam_id in self._last_results:
                            last = dict(self._last_results[cam_id])
                            all_results.append(last)
                        continue
                    data = None
                    try:
                        while not q.empty():
                            data = q.get_nowait()
                    except Exception:
                        pass
                    if data is None:
                        continue
                    frame = data['frame']
                    # Decidir se roda análise neste frame
                    c = self._cam_counter.get(cam_id, 0) + 1
                    self._cam_counter[cam_id] = c
                    do_analysis = (c % max(1, int(self._cam_stride))) == 0
                    if do_analysis:
                        res = self.system.process_frame(cam_id, frame)
                        # garantir que o frame usado para exibição é o atual
                        res['frame'] = frame
                        self._last_results[cam_id] = res
                        updated_cameras.add(cam_id)
                        all_results.append(res)
                    else:
                        # Reusar último resultado, mas com o frame atual
                        if cam_id in self._last_results:
                            fast = dict(self._last_results[cam_id])
                            fast['frame'] = frame
                            all_results.append(fast)
                        else:
                            # Se ainda não há resultado, cria mínimo
                            all_results.append({'camera_id': cam_id, 'faces': [], 'frame': frame, 'timestamp': time.time()})
                    self.system.total_frames += 1

                # Agendar análises assíncronas (não bloquear UI)
                for cam in self.system.cameras:
                    cam_id = cam.source_id
                    # Só agendar se houver frame recente no último ciclo e sem análise em andamento
                    if cam_id in self._analysis_inflight:
                        continue
                    # Respeitar intervalo mínimo
                    last_ts = self._last_analysis_ts.get(cam_id, 0.0)
                    if (time.time() - last_ts) < self._analysis_min_interval:
                        continue
                    # Verificar se possuímos um frame atual exibido neste ciclo
                    # (usa _last_results como proxy; se não há, ainda assim agenda uma inicial)
                    try:
                        # Inserir tarefa leve: somente o frame mais recente da câmera
                        # Pegamos diretamente do dict de resultados deste ciclo
                        # (encontrar correspondente em all_results)
                        found = None
                        for r in all_results:
                            if r.get('camera_id') == cam_id:
                                found = r
                                break
                        if found is None:
                            continue
                        frame = found.get('frame')
                        if frame is None:
                            continue
                        if not self._analysis_queue.full():
                            self._analysis_inflight.add(cam_id)
                            self._analysis_queue.put_nowait((cam_id, frame))
                    except Exception:
                        pass

                if all_results:
                    # Renderizar quadro combinado com resolução fixa (evita flicker)
                    frame = self.system.create_display_frame(
                        all_results,
                        display_width=self._display_w,
                        display_height=self._display_h,
                    )
                    self._current_frame_bgr = frame
                    # Converter para PIL no worker; UI aplica depois
                    try:
                        img_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                        self._pending_pil = Image.fromarray(img_rgb)
                    except Exception:
                        pass

                    # Atualizar estatísticas (postar para o ticker da UI)
                    try:
                        self._pending_stats = self.system.get_system_stats()
                    except Exception:
                        pass

                    # Notificações (postar para o ticker da UI)
                    try:
                        self._pending_notifications = self.system.notification_system.get_recent_notifications(10)
                    except Exception:
                        pass

                time.sleep(0.008)
        except Exception:
            # Em caso de erro no loop, garantir que a UI não trave
            pass

    def _enqueue_recognition(self, name: str, conf: float, emotion: Any, age: Any, gender: Any) -> None:
        ts = datetime.now().strftime('%H:%M:%S')
        row = (ts, name, f"{conf:.2f}", str(emotion) if isinstance(emotion, str) else getattr(emotion, 'emotion', str(emotion)), str(age), str(gender))
        self.recognition_events.insert(0, row)
        if len(self.recognition_events) > 200:
            self.recognition_events = self.recognition_events[:200]
        # Atualizar tabela rapidamente
        def _apply():
            try:
                self.tree_rec.insert('', 'end', values=row)
                # Manter no máximo 400 linhas visuais
                children = self.tree_rec.get_children()
                if len(children) > 400:
                    self.tree_rec.delete(children[0])
            except Exception:
                pass
        self.root.after(0, _apply)

    def _update_stats(self, stats: Dict[str, Any]) -> None:
        try:
            self.lbl_fps.config(text=f"FPS: {stats.get('fps', 0):.1f}")
            self.lbl_rec.config(text=f"Reconhecimentos: {stats.get('total_recognitions', 0)}")
            self.lbl_mem.config(text=f"Memória: {stats.get('memory_usage', 0):.1f}%")
            # Atualizar presença
            self._refresh_attendance()
        except Exception:
            pass

    def _apply_pil_image(self, pil_image: Image.Image) -> None:
        """Criar PhotoImage no thread principal e aplicar no label sem flicker."""
        try:
            imgtk = ImageTk.PhotoImage(image=pil_image)
            self._display_image = imgtk  # manter referência viva
            self.video_label.configure(image=imgtk, text='')
            # também manter no próprio widget (garantido)
            self.video_label.image = imgtk
        except Exception:
            pass

    def _analysis_loop(self) -> None:
        """Processar reconhecimento em thread separada por câmera (não bloquear exibição)."""
        while self.running and self.system is not None:
            try:
                try:
                    cam_id, frame = self._analysis_queue.get(timeout=0.05)
                except Exception:
                    continue
                try:
                    res = self.system.process_frame(cam_id, frame)
                    # Guardar resultados
                    self._last_results[cam_id] = res
                    self._last_analysis_ts[cam_id] = time.time()
                    # Publicar reconhecimentos no UI thread
                    for f in res.get('faces', []):
                        name = f.get('name')
                        conf = float(f.get('confidence', 0.0))
                        emo = f.get('emotion')
                        age = f.get('age')
                        gen = f.get('gender')
                        if name:
                            self.root.after(0, self._enqueue_recognition, name, conf, emo, age, gen)
                    # Atualizações pendentes para ticker
                    try:
                        self._pending_stats = self.system.get_system_stats()
                    except Exception:
                        pass
                    try:
                        self._pending_notifications = self.system.notification_system.get_recent_notifications(10)
                    except Exception:
                        pass
                finally:
                    if cam_id in self._analysis_inflight:
                        self._analysis_inflight.remove(cam_id)
            except Exception:
                # Nunca travar esse laço
                time.sleep(0.01)

    def _ui_tick(self) -> None:
        """Atualizações periódicas na thread principal (30 FPS alvo)."""
        try:
            if self._pending_pil is not None:
                pil = self._pending_pil
                self._pending_pil = None
                self._apply_pil_image(pil)
            if self._pending_stats is not None:
                s = self._pending_stats
                self._pending_stats = None
                self._update_stats(s)
            if self._pending_notifications is not None:
                n = self._pending_notifications
                self._pending_notifications = None
                self._update_notifications(n)
            # Diagnóstico
            self._refresh_diagnostics()
        except Exception:
            pass
        finally:
            try:
                self.root.after(self._ui_interval_ms, self._ui_tick)
            except Exception:
                pass

    def _measure_display_size(self) -> None:
        """Medir label de vídeo e definir resolução alvo fixa."""
        try:
            w = int(self.video_label.winfo_width())
            h = int(self.video_label.winfo_height())
            if w > 0 and h > 0:
                # Converter para 16:9 mantendo dentro do espaço
                target_w = max(960, w)
                target_h = int(target_w * 9 / 16)
                # Não exagerar para não pesar
                if target_w > 1600:
                    target_w = 1600
                    target_h = int(target_w * 9 / 16)
                self._display_w = target_w
                self._display_h = target_h
        except Exception:
            pass

    def _refresh_diagnostics(self) -> None:
        try:
            # Montar texto de diagnóstico
            lines: List[str] = []
            if self.system is not None:
                try:
                    stats = self.system.get_system_stats()
                    lines.append(f"FPS (global): {stats.get('fps',0):.1f}")
                    lines.append(f"Reconhecimentos: {stats.get('total_recognitions',0)}  Câmeras ativas: {stats.get('active_cameras',0)}")
                    lines.append(f"CPU: {stats.get('cpu_usage',0)}%  Memória: {stats.get('memory_usage',0):.1f}%")
                except Exception:
                    pass
                # Por câmera
                for cam in getattr(self.system, 'cameras', []):
                    try:
                        q = self.system.frame_queues.get(cam.source_id)
                        qlen = q.qsize() if q is not None else 0
                        lines.append(f"{cam.source_id}: fps_cam={cam.fps:.1f} queue={qlen}")
                    except Exception:
                        continue
            # Backends
            try:
                import tensorflow as _tf  # type: ignore
                gpus = _tf.config.experimental.list_physical_devices('GPU')
                lines.append(f"TensorFlow: {_tf.__version__}  GPU: {len(gpus)}")
            except Exception:
                lines.append("TensorFlow: não disponível")
            try:
                import onnxruntime as _ort  # type: ignore
                lines.append(f"ONNXRuntime: {_ort.__version__}")
            except Exception:
                lines.append("ONNXRuntime: não disponível")
            # Aplicar no Text
            current = self.diag_text.get('1.0', 'end-1c') if self.diag_text.winfo_exists() else ''
            out = '\n'.join(lines)
            if out != current:
                self.diag_text.delete('1.0', 'end')
                self.diag_text.insert('1.0', out)
        except Exception:
            pass

    def _update_notifications(self, notifications: List[Dict[str, Any]]) -> None:
        try:
            self.list_not.delete(0, tk.END)
            for n in notifications:
                t = datetime.fromisoformat(n['timestamp']).strftime('%H:%M:%S')
                self.list_not.insert(tk.END, f"[{t}] {n.get('icon','')} {n.get('message','')}")
        except Exception:
            pass

    # ---------- Dados / Config ----------
    def _load_camera_settings(self) -> None:
        self.camera_cfg = {
            'webcam_enabled': True,
            'c100_enabled': False,
            'c100_url': '',
            'webcam_device': 0,
            'webcam_backend': 'CAP_DSHOW'
        }
        try:
            if os.path.exists('camera_settings.json'):
                with open('camera_settings.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                self.camera_cfg.update({
                    'webcam_enabled': bool(data.get('webcam_enabled', True)),
                    'c100_enabled': bool(data.get('c100_enabled', False)),
                    'c100_url': data.get('c100_url', ''),
                    'webcam_device': int(data.get('webcam_device', 0)),
                    'webcam_backend': data.get('webcam_backend', 'CAP_DSHOW')
                })
            if self.camera_cfg.get('c100_url'):
                os.environ['RTSP_C100_URL'] = self.camera_cfg['c100_url']
        except Exception:
            pass

    def _get_cam_cfg(self, key: str, default: Any) -> Any:
        try:
            return self.camera_cfg.get(key, default)
        except Exception:
            return default

    def open_camera_config(self) -> None:
        dlg = tk.Toplevel(self.root)
        dlg.title('Configurar Câmeras')
        dlg.grab_set()
        v_web = tk.BooleanVar(value=self.camera_cfg.get('webcam_enabled', True))
        v_c100 = tk.BooleanVar(value=self.camera_cfg.get('c100_enabled', False))
        v_url = tk.StringVar(value=self.camera_cfg.get('c100_url', os.getenv('RTSP_C100_URL', '')))
        ttk.Checkbutton(dlg, text='Habilitar Webcam (local 0)', variable=v_web).grid(row=0,column=0,columnspan=2,sticky='w',padx=10,pady=8)
        ttk.Checkbutton(dlg, text='Habilitar C100 (RTSP)', variable=v_c100).grid(row=1,column=0,columnspan=2,sticky='w',padx=10)
        ttk.Label(dlg, text='RTSP C100 URL:').grid(row=2,column=0,sticky='e',padx=10,pady=8)
        ttk.Entry(dlg, textvariable=v_url, width=60).grid(row=2,column=1,sticky='w',padx=10)
        def save_close():
            try:
                self.camera_cfg = {
                    'webcam_enabled': bool(v_web.get()),
                    'c100_enabled': bool(v_c100.get()),
                    'c100_url': v_url.get().strip()
                }
                with open('camera_settings.json', 'w', encoding='utf-8') as f:
                    json.dump(self.camera_cfg, f, indent=2, ensure_ascii=False)
                if self.camera_cfg.get('c100_url'):
                    os.environ['RTSP_C100_URL'] = self.camera_cfg['c100_url']
            finally:
                dlg.destroy()
        btns = ttk.Frame(dlg)
        btns.grid(row=3, column=0, columnspan=2, pady=10)
        ttk.Button(btns, text='Salvar', command=save_close).pack(side='left', padx=5)
        ttk.Button(btns, text='Cancelar', command=dlg.destroy).pack(side='left', padx=5)

    def rebuild_embeddings(self) -> None:
        try:
            if not messagebox.askyesno('Recriar', 'Recriar embeddings a partir de known_faces?'):
                return
            # Instância temporária só para regenerar
            fr = FaceRecognizer(
                known_faces_dir=self.config.known_faces_dir,
                embeddings_file=self.config.embeddings_file,
                model_name=self.config.model.name,
                threshold=self.config.model.threshold,
            )
            ok = fr.create_embeddings()
            if ok:
                messagebox.showinfo('Embeddings', 'Embeddings recriados com sucesso.')
            else:
                messagebox.showwarning('Embeddings', 'Nenhuma imagem processada em known_faces/.')
        except Exception as e:
            messagebox.showerror('Erro', f'Falha ao recriar embeddings: {e}')

    def add_person(self) -> None:
        """Registrar pessoa com captura ao vivo e critérios de qualidade automáticos."""
        dlg = tk.Toplevel(self.root)
        dlg.title('Registrar Pessoa — Captura Automática')
        dlg.grab_set()
        dlg.geometry('820x620')

        v_name = tk.StringVar()
        ttk.Label(dlg, text='Nome:').grid(row=0, column=0, sticky='e', padx=10, pady=8)
        ttk.Entry(dlg, textvariable=v_name, width=32).grid(row=0, column=1, sticky='w')

        # Área de vídeo e métricas
        vid_frame = tk.Frame(dlg, bg='black', bd=1, relief='sunken')
        vid_frame.grid(row=1, column=0, columnspan=3, sticky='nsew', padx=10, pady=8)
        dlg.columnconfigure(1, weight=1)
        dlg.rowconfigure(1, weight=1)
        video_label = tk.Label(vid_frame, bg='black')
        video_label.pack(fill='both', expand=True)

        metrics = ttk.Frame(dlg)
        metrics.grid(row=2, column=0, columnspan=3, sticky='ew', padx=10)
        lbl_state = ttk.Label(metrics, text='Posicione o rosto dentro do quadro. O sistema capturará automaticamente quando a qualidade estiver boa.')
        lbl_state.grid(row=0, column=0, columnspan=3, sticky='w')
        # Gauges
        ttk.Label(metrics, text='Luz').grid(row=1, column=0, sticky='e')
        pb_light = ttk.Progressbar(metrics, length=220, mode='determinate', maximum=255)
        pb_light.grid(row=1, column=1, sticky='w', padx=8)
        ttk.Label(metrics, text='Nitidez').grid(row=2, column=0, sticky='e')
        pb_sharp = ttk.Progressbar(metrics, length=220, mode='determinate', maximum=500)
        pb_sharp.grid(row=2, column=1, sticky='w', padx=8)
        ttk.Label(metrics, text='Estabilidade').grid(row=3, column=0, sticky='e')
        pb_stable = ttk.Progressbar(metrics, length=220, mode='determinate', maximum=12)
        pb_stable.grid(row=3, column=1, sticky='w', padx=8)
        lbl_info = ttk.Label(metrics, text='Qualidade: -- | Iluminação: -- | Nitidez: -- | Tamanho: -- | Centralização: --')
        lbl_info.grid(row=4, column=0, columnspan=3, sticky='w', pady=(4,0))

        # Botões
        btns = ttk.Frame(dlg)
        btns.grid(row=3, column=0, columnspan=3, pady=10)
        btn_cancel = ttk.Button(btns, text='Cancelar', command=lambda: _stop(True))
        btn_manual = ttk.Button(btns, text='Capturar Agora', command=lambda: _force_capture())
        btn_cancel.pack(side='left', padx=5)
        btn_manual.pack(side='left', padx=5)

        # Estado da captura
        running = True
        captured = {'done': False}
        stable_count = 0
        stable_needed = 12  # ~0.4s a 30fps
        good_samples: List[Dict[str, Any]] = []
        target_samples = 3

        # Detector rápido para captura
        detector = FaceDetector(detector_backend='opencv', confidence_threshold=0.4)

        # Abrir webcam
        try:
            cap = cv2.VideoCapture(0, cv2.CAP_DSHOW)
        except Exception:
            cap = cv2.VideoCapture(0)
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
        try:
            cap.set(cv2.CAP_PROP_FPS, 30)
            cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc(*'MJPG'))
            cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)
        except Exception:
            pass

        def _sanitize(s: str) -> str:
            import re
            s = s.strip()
            s = re.sub(r'[^\w\- ]+', '', s)
            s = s.replace(' ', '_')
            return s

        def _save_face(face_img_bgr):
            name = v_name.get().strip()
            if not name:
                dlg.after(0, lambda: messagebox.showerror('Nome', 'Informe o nome da pessoa.'))
                return False
            try:
                os.makedirs(self.config.known_faces_dir, exist_ok=True)
                fname = _sanitize(name) or 'pessoa'
                dest = os.path.join(self.config.known_faces_dir, f'{fname}.jpg')
                # Garantir JPEG
                cv2.imwrite(dest, face_img_bgr)
                # Atualizar reconhecedor
                if self.system:
                    try:
                        self.system.face_recognizer.add_person(name, face_img_bgr)
                    except Exception:
                        pass
                dlg.after(0, lambda: messagebox.showinfo('Registro', f'Pessoa {name} registrada com sucesso!'))
                return True
            except Exception as e:
                dlg.after(0, lambda: messagebox.showerror('Erro', f'Falha ao salvar: {e}'))
                return False

        def _force_capture():
            # Tenta capturar o quadro atual (sem garantias de qualidade)
            try:
                ret, frame = cap.read()
                if not ret:
                    return
                # Detectar e recortar maior face
                faces = detector.detect_faces(frame)
                if not faces:
                    messagebox.showwarning('Captura', 'Nenhum rosto detectado.')
                    return
                # Escolher maior bbox
                fb = max(faces, key=lambda f: f.get('bbox', (0,0,0,0))[2]*f.get('bbox', (0,0,0,0))[3]).get('bbox')
                x,y,w,h = fb
                # margem
                mx = int(w*0.15); my = int(h*0.20)
                x0 = max(0, x-mx); y0 = max(0, y-my)
                x1 = min(frame.shape[1], x+w+mx); y1 = min(frame.shape[0], y+h+my)
                face_img = frame[y0:y1, x0:x1]
                # Normalizar tamanho
                face_img = cv2.resize(face_img, (320, 320))
                if _save_face(face_img):
                    _stop()
            except Exception:
                pass

        def _stop(cancel: bool=False):
            nonlocal running
            running = False
            try:
                cap.release()
            except Exception:
                pass
            if not cancel and not captured.get('done'):
                # nada
                pass
            try:
                dlg.destroy()
            except Exception:
                pass

        def _apply_frame(pil_img):
            try:
                imgtk = ImageTk.PhotoImage(image=pil_img)
                video_label.configure(image=imgtk)
                video_label.image = imgtk
            except Exception:
                pass

        def _safe_config(widget, **kwargs):
            try:
                if widget.winfo_exists():
                    widget.configure(**kwargs)
            except Exception:
                pass

        def _safe_set_pb(pb, value):
            try:
                if pb.winfo_exists():
                    pb.configure(value=value)
            except Exception:
                pass

        def _loop():
            nonlocal stable_count
            while running:
                # Se o diálogo foi fechado, encerrar loop com segurança
                try:
                    if not dlg.winfo_exists():
                        break
                except Exception:
                    break
                ret, frame = cap.read()
                if not ret:
                    time.sleep(0.01)
                    continue
                # Pequena estabilização
                frame = cv2.flip(frame, 1)
                h, w = frame.shape[:2]
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                brightness = float(gray.mean())
                sharpness = float(cv2.Laplacian(gray, cv2.CV_64F).var())
                faces = detector.detect_faces(frame)
                # Critérios
                ok_one_face = len(faces) == 1
                face_bbox = None
                if faces:
                    face_bbox = faces[0].get('bbox')
                size_ok = center_ok = False
                if face_bbox is not None:
                    x, y, fw, fh = face_bbox
                    area_ratio = (fw*fh) / float(w*h)
                    size_ok = 0.15 <= area_ratio <= 0.55
                    cx = x + fw/2.0; cy = y + fh/2.0
                    dx = abs(cx - w/2.0) / (w/2.0)
                    dy = abs(cy - h/2.0) / (h/2.0)
                    center_ok = dx < 0.25 and dy < 0.25
                light_ok = 80 <= brightness <= 200
                sharp_ok = sharpness >= 90
                all_ok = ok_one_face and size_ok and center_ok and light_ok and sharp_ok
                if all_ok:
                    stable_count += 1
                else:
                    stable_count = 0

                # Desenhar overlay (painel superior sem sobrepor rodapé)
                disp = frame.copy()
                # guia de enquadramento (área alvo aproximada)
                guide_w = int(w * 0.38)
                guide_h = int(h * 0.50)
                gx0 = int((w - guide_w) / 2)
                gy0 = int((h - guide_h) / 2)
                gx1 = gx0 + guide_w
                gy1 = gy0 + guide_h
                cv2.rectangle(disp, (gx0, gy0), (gx1, gy1), (180, 180, 180), 1)
                # bbox do rosto
                color = (0,255,0) if all_ok else (0,165,255) if ok_one_face else (0,0,255)
                if face_bbox is not None:
                    x,y,fw,fh = face_bbox
                    cv2.rectangle(disp, (int(x),int(y)), (int(x+fw),int(y+fh)), color, 2)
                # barra superior semitransparente
                overlay = disp.copy()
                bar_h = 64
                cv2.rectangle(overlay, (0,0), (w, bar_h), (0,0,0), -1)
                cv2.addWeighted(overlay, 0.55, disp, 0.45, 0, disp)
                # textos no topo
                txt1 = f"Luz:{int(brightness)} {'✔' if light_ok else '✖'}  " \
                       f"Nitidez:{int(sharpness)} {'✔' if sharp_ok else '✖'}  Rostos:{len(faces)}"
                txt2 = f"Tamanho:{'✔' if size_ok else '✖'}  Centro:{'✔' if center_ok else '✖'}"
                cv2.putText(disp, txt1, (10, 24), cv2.FONT_HERSHEY_SIMPLEX, 0.65, (255,255,255), 2)
                cv2.putText(disp, txt2, (10, 48), cv2.FONT_HERSHEY_SIMPLEX, 0.65, (255,255,255), 2)
                if stable_count>0:
                    cv2.putText(disp, f"Capturando em {max(0, stable_needed-stable_count)}...",
                                (w-280, 32), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0,255,0), 2)

                # Atualizar texto e gauges (métricas abaixo do preview)
                try:
                    status_line = f"Qualidade: {'BOA' if all_ok else 'AJUSTE POSIÇÃO/ILUMINAÇÃO'} | " \
                                  f"Luz:{'OK' if light_ok else 'X'} | Nitidez:{'OK' if sharp_ok else 'X'} | " \
                                  f"Tamanho:{'OK' if size_ok else 'X'} | Centro:{'OK' if center_ok else 'X'} | Rostos:{len(faces)}"
                    dlg.after(0, _safe_config, lbl_info, text=status_line)
                    dlg.after(0, _safe_set_pb, pb_light, int(brightness))
                    dlg.after(0, _safe_set_pb, pb_sharp, min(int(sharpness),500))
                    dlg.after(0, _safe_set_pb, pb_stable, min(stable_count,stable_needed))
                except Exception:
                    pass

                # Publicar na UI
                try:
                    rgb = cv2.cvtColor(disp, cv2.COLOR_BGR2RGB)
                    pil = Image.fromarray(rgb)
                    dlg.after(0, _apply_frame, pil)
                except Exception:
                    pass

                # Auto-captura quando estável o suficiente
                if not captured['done'] and stable_count >= stable_needed and face_bbox is not None:
                    x,y,fw,fh = face_bbox
                    mx = int(fw*0.15); my = int(fh*0.20)
                    x0 = max(0, int(x-mx)); y0 = max(0, int(y-my))
                    x1 = min(w, int(x+fw+mx)); y1 = min(h, int(y+fh+my))
                    face_img = frame[y0:y1, x0:x1]
                    face_img = cv2.resize(face_img, (320,320))
                    # Guardar amostra (até 3)
                    sample = {'img': face_img.copy(), 'sharp': float(sharpness)}
                    # Extrair embedding para seleção/ média (se disponível)
                    try:
                        if self.system:
                            emb = self.system.face_recognizer.embedding_extractor.extract_embedding(face_img)
                            sample['emb'] = emb
                    except Exception:
                        pass
                    good_samples.append(sample)
                    stable_count = 0
                    if len(good_samples) >= target_samples:
                        # Selecionar melhor por nitidez
                        best = max([s for s in good_samples if 'img' in s], key=lambda s: s.get('sharp',0.0))
                        saved_ok = _save_face(best['img'])
                        # Atualizar com média de embeddings, se possível
                        try:
                            embs = [s.get('emb') for s in good_samples if s.get('emb') is not None]
                            if saved_ok and self.system and embs:
                                import numpy as _np
                                avg = _np.mean(_np.stack(embs, axis=0), axis=0)
                                rec = self.system.face_recognizer
                                name = v_name.get().strip()
                                rec.known_embeddings[name] = avg
                                if name not in rec.person_names:
                                    rec.person_names.append(name)
                                rec.save_embeddings()
                        except Exception:
                            pass
                        if saved_ok:
                            captured['done'] = True
                            dlg.after(0, _stop)
                            break

                time.sleep(0.01)

        t = threading.Thread(target=_loop, daemon=True)
        t.start()

    def add_person_upload(self) -> None:
        """Registrar pessoa via upload de foto com corte automático do rosto."""
        dlg = tk.Toplevel(self.root)
        dlg.title('Registrar Pessoa — Upload de Foto')
        dlg.grab_set()
        dlg.geometry('720x520')

        v_name = tk.StringVar()
        v_path = tk.StringVar()

        ttk.Label(dlg, text='Nome:').grid(row=0, column=0, sticky='e', padx=10, pady=8)
        ttk.Entry(dlg, textvariable=v_name, width=40).grid(row=0, column=1, sticky='w')

        ttk.Label(dlg, text='Imagem:').grid(row=1, column=0, sticky='e', padx=10, pady=8)
        ttk.Entry(dlg, textvariable=v_path, width=40).grid(row=1, column=1, sticky='w')

        preview = tk.Label(dlg, bg='black')
        preview.grid(row=2, column=0, columnspan=3, sticky='nsew', padx=10, pady=10)
        dlg.columnconfigure(1, weight=1)
        dlg.rowconfigure(2, weight=1)

        detector = FaceDetector(detector_backend='opencv', confidence_threshold=0.4)

        def _sanitize(s: str) -> str:
            import re
            s = s.strip()
            s = re.sub(r'[^\w\- ]+', '', s)
            s = s.replace(' ', '_')
            return s

        def browse():
            fn = filedialog.askopenfilename(filetypes=[('Imagens','*.jpg;*.jpeg;*.png;*.bmp;*.tiff')])
            if not fn:
                return
            v_path.set(fn)
            try:
                img = cv2.imread(fn)
                if img is None:
                    messagebox.showerror('Imagem', 'Não foi possível ler a imagem.')
                    return
                faces = detector.detect_faces(img)
                if faces:
                    fb = max(faces, key=lambda f: f.get('bbox', (0,0,0,0))[2]*f.get('bbox', (0,0,0,0))[3]).get('bbox')
                    x,y,w,h = fb
                    mx = int(w*0.15); my = int(h*0.20)
                    x0 = max(0, x-mx); y0 = max(0, y-my)
                    x1 = min(img.shape[1], x+w+mx); y1 = min(img.shape[0], y+h+my)
                    roi = img[y0:y1, x0:x1]
                else:
                    roi = img
                roi = cv2.resize(roi, (320,320))
                rgb = cv2.cvtColor(roi, cv2.COLOR_BGR2RGB)
                pil = Image.fromarray(rgb)
                imgtk = ImageTk.PhotoImage(pil)
                preview.configure(image=imgtk)
                preview.image = imgtk
                preview._roi_bgr = roi
            except Exception as e:
                messagebox.showerror('Erro', f'Falha ao preparar preview: {e}')

        def save():
            name = (v_name.get() or '').strip()
            path = (v_path.get() or '').strip()
            if not name:
                messagebox.showerror('Nome', 'Informe o nome da pessoa.')
                return
            if not path or not os.path.exists(path):
                messagebox.showerror('Imagem', 'Selecione uma imagem válida.')
                return
            try:
                face_img = getattr(preview, '_roi_bgr', None)
                if face_img is None:
                    img = cv2.imread(path)
                    if img is None:
                        messagebox.showerror('Imagem', 'Não foi possível ler a imagem.')
                        return
                    faces = detector.detect_faces(img)
                    if faces:
                        fb = max(faces, key=lambda f: f.get('bbox', (0,0,0,0))[2]*f.get('bbox', (0,0,0,0))[3]).get('bbox')
                        x,y,w,h = fb
                        mx = int(w*0.15); my = int(h*0.20)
                        x0 = max(0, x-mx); y0 = max(0, y-my)
                        x1 = min(img.shape[1], x+w+mx); y1 = min(img.shape[0], y+h+my)
                        face_img = img[y0:y1, x0:x1]
                    else:
                        face_img = img
                    face_img = cv2.resize(face_img, (320,320))
                os.makedirs(self.config.known_faces_dir, exist_ok=True)
                fname = _sanitize(name) or 'pessoa'
                dest = os.path.join(self.config.known_faces_dir, f'{fname}.jpg')
                cv2.imwrite(dest, face_img)
                if self.system:
                    try:
                        self.system.face_recognizer.add_person(name, face_img)
                    except Exception:
                        pass
                messagebox.showinfo('Registro', f'Pessoa {name} registrada com sucesso!')
                dlg.destroy()
            except Exception as e:
                messagebox.showerror('Erro', f'Falha ao salvar: {e}')

        ttk.Button(dlg, text='Escolher...', command=browse).grid(row=1, column=2, padx=6)
        btns = ttk.Frame(dlg)
        btns.grid(row=3, column=0, columnspan=3, pady=10)
        ttk.Button(btns, text='Salvar', command=save).pack(side='left', padx=5)
        ttk.Button(btns, text='Cancelar', command=dlg.destroy).pack(side='left', padx=5)

    def manage_people(self) -> None:
        if not self.system:
            messagebox.showinfo('Info', 'Inicie o sistema para gerenciar em tempo real. (Também é possível editar known_faces/)')
        top = tk.Toplevel(self.root)
        top.title('Pessoas Conhecidas')
        top.geometry('380x300')
        top.grab_set()
        lb = tk.Listbox(top)
        lb.pack(fill='both', expand=True, padx=10, pady=10)
        people = []
        try:
            if self.system:
                people = sorted(self.system.face_recognizer.known_embeddings.keys())
        except Exception:
            pass
        for p in people:
            lb.insert(tk.END, p)
        def remove_selected():
            sel = lb.curselection()
            if not sel:
                return
            name = lb.get(sel[0])
            ok = False
            try:
                if self.system:
                    ok = self.system.face_recognizer.remove_person(name)
            except Exception:
                pass
            # Remover arquivo
            try:
                for ext in ('.jpg','.jpeg','.png','.bmp','.tiff'):
                    fp = os.path.join(self.config.known_faces_dir, f"{name}{ext}")
                    if os.path.exists(fp):
                        os.remove(fp)
            except Exception:
                pass
            if ok:
                lb.delete(sel[0])
        btns = ttk.Frame(top)
        btns.pack(pady=6)
        ttk.Button(btns, text='Remover Selecionado', command=remove_selected).pack(side='left', padx=5)
        ttk.Button(btns, text='Fechar', command=top.destroy).pack(side='left', padx=5)

    def export_data(self) -> None:
        fn = filedialog.asksaveasfilename(defaultextension='.zip', filetypes=[('ZIP','*.zip')])
        if not fn:
            return
        try:
            import zipfile
            with zipfile.ZipFile(fn, 'w', compression=zipfile.ZIP_DEFLATED) as zf:
                if os.path.exists(self.config.embeddings_file):
                    zf.write(self.config.embeddings_file)
                if os.path.exists('attendance.json'):
                    zf.write('attendance.json')
                if os.path.isdir(self.config.known_faces_dir):
                    for root, _, files in os.walk(self.config.known_faces_dir):
                        for f in files:
                            zf.write(os.path.join(root, f))
            messagebox.showinfo('Exportar', f'Dados exportados: {fn}')
        except Exception as e:
            messagebox.showerror('Erro', f'Falha ao exportar: {e}')

    def import_data(self) -> None:
        fn = filedialog.askopenfilename(filetypes=[('ZIP','*.zip')])
        if not fn:
            return
        try:
            import zipfile
            with zipfile.ZipFile(fn, 'r') as zf:
                zf.extractall('.')
            messagebox.showinfo('Importar', 'Importação concluída. Se necessário, reinicie o reconhecimento.')
        except Exception as e:
            messagebox.showerror('Erro', f'Falha ao importar: {e}')

    def export_analytics(self) -> None:
        try:
            if not self.system:
                messagebox.showinfo('Info', 'Inicie o sistema para exportar analytics.')
                return
            fn = self.system.analytics.export_analytics()
            messagebox.showinfo('Analytics', f'Exportado: {fn}')
        except Exception as e:
            messagebox.showerror('Erro', f'Falha: {e}')

    def attendance_report(self) -> None:
        try:
            if not self.system:
                messagebox.showinfo('Relatório', 'Inicie o sistema para ver presenças.')
                return
            today = self.system.attendance_tracker.get_today_attendance()
            messagebox.showinfo('Presenças', f'Registros hoje: {len(today)}')
        except Exception:
            pass

    def _refresh_attendance(self) -> None:
        try:
            if not self.system:
                return
            for item in self.tree_att.get_children():
                self.tree_att.delete(item)
            for rec in reversed(self.system.attendance_tracker.get_today_attendance()[-50:]):
                t = datetime.fromisoformat(rec['timestamp']).strftime('%H:%M:%S')
                self.tree_att.insert('', 'end', values=(t, rec['person'], f"{rec['confidence']:.2f}"))
        except Exception:
            pass

    # ---------- Layout helpers ----------
    def set_side_panel_ratio(self, ratio: float) -> None:
        try:
            r = float(ratio)
            if r < 0.5:
                r = 0.5
            if r > 0.9:
                r = 0.9
            self._sash_ratio = r
            self._set_sash_by_ratio()
        except Exception:
            pass

    def set_analysis_stride(self, stride: int) -> None:
        """Ajustar quantos frames pular entre análises (maior = mais fluido)."""
        try:
            s = int(stride)
            if s < 1:
                s = 1
            if s > 8:
                s = 8
            self._cam_stride = s
        except Exception:
            pass

    def _on_paned_configure(self, event=None) -> None:
        if getattr(self, '_setting_sash', False):
            return
        self._set_sash_by_ratio()

    def _set_sash_by_ratio(self) -> None:
        try:
            self.root.update_idletasks()
            total = int(self.paned.winfo_width())
            if total <= 0:
                total = int(self.root.winfo_width())
            if total <= 0:
                return
            pos = int(total * self._sash_ratio)
            # mínimos visuais
            min_left = 640
            min_right = 360
            if total - pos < min_right:
                pos = total - min_right
            if pos < min_left:
                pos = min_left
            self._setting_sash = True
            try:
                self.paned.sashpos(0, pos)
            finally:
                self._setting_sash = False
        except Exception:
            pass

    # ---------- Janela ----------
    def toggle_fullscreen(self) -> None:
        try:
            self.root.attributes('-fullscreen', True)
        except Exception:
            pass

    def exit_fullscreen(self) -> None:
        try:
            self.root.attributes('-fullscreen', False)
        except Exception:
            pass

    def show_about(self) -> None:
        messagebox.showinfo("Sobre", "Nova Interface Escola Segura\nPython + Tkinter\nLayout simplificado e robusto.")

    def on_close(self) -> None:
        try:
            self.running = False
            if self.system:
                self.system.stop()
        finally:
            self.root.destroy()

    def run(self) -> None:
        self.root.mainloop()

    # ---------- GPU helpers ----------
    def _setup_gpu(self) -> None:
        """Habilitar uso de GPU (CUDA) quando disponível via TensorFlow/DeepFace."""
        try:
            # Respeitar flag do config se existir
            enable_gpu = True
            try:
                enable_gpu = bool(getattr(self.config.processing, 'enable_gpu', True))
            except Exception:
                pass
            if not enable_gpu:
                return
            # TensorFlow (DeepFace usa TF por padrão)
            import tensorflow as tf  # type: ignore
            gpus = tf.config.experimental.list_physical_devices('GPU')
            if gpus:
                for gpu in gpus:
                    try:
                        tf.config.experimental.set_memory_growth(gpu, True)
                    except Exception:
                        pass
        except Exception:
            # GPU não disponível ou TF não instalado com suporte CUDA
            pass


def main() -> None:
    app = EscolaSeguraApp()
    app.run()


if __name__ == '__main__':
    main()
