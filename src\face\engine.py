import os
from typing import List, Dict, Optional, Tuple, Any

import numpy as np
import cv2

from insightface.app import FaceAnalysis
from insightface.utils import face_align


def _parse_providers() -> List[str]:
    # Try to use GPU provider if onnxruntime-gpu is installed; fallback to CPU
    providers = []
    try:
        import onnxruntime as ort  # noqa: F401
        # Prefer CUDA if available
        providers.append("CUDAExecutionProvider")
    except Exception:
        pass
    providers.append("CPUExecutionProvider")
    return providers


class FaceEngine:
    def __init__(
        self,
        model_pack: str = "buffalo_l",
        det_size: Tuple[int, int] = (640, 640),
        max_size: Optional[int] = None,
    ) -> None:
        self.providers = _parse_providers()
        self.app = FaceAnalysis(name=model_pack, providers=self.providers)
        # ctx_id is ignored for ORT backend but kept for compatibility
        self.app.prepare(ctx_id=0, det_size=det_size)
        self.det_size = det_size
        self.max_size = max_size

    def _maybe_resize(self, frame: np.ndarray, target_max: Optional[int]) -> Tuple[np.ndarray, float]:
        if target_max is None:
            return frame, 1.0
        h, w = frame.shape[:2]
        scale = 1.0
        if max(h, w) > target_max:
            scale = target_max / float(max(h, w))
            new_w = int(w * scale)
            new_h = int(h * scale)
            frame = cv2.resize(frame, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
        return frame, scale

    def detect(self, frame: np.ndarray) -> List[Any]:
        return self.app.get(frame)

    def detect_and_embed(self, frame: np.ndarray) -> List[Dict[str, Any]]:
        """
        Returns list of dicts with keys: bbox, kps, det_score, embedding, chip
        chip: aligned 112x112 RGB face
        """
        if frame is None:
            return []
        img = frame
        faces = self.app.get(img)
        out = []
        for f in faces:
            bbox = f.bbox.astype(np.int32)
            kps = f.kps.astype(np.float32)
            det_score = float(f.det_score)
            # Aligned chip
            chip = face_align.norm_crop(img, landmark=kps, image_size=112)
            emb = f.embedding.astype(np.float32)
            # embedding from insightface is already L2-normalized
            out.append({
                "bbox": bbox,
                "kps": kps,
                "det_score": det_score,
                "embedding": emb,
                "chip": chip,
            })
        return out

