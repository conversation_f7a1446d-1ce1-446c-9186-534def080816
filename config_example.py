# Configuração de exemplo para o sistema de reconhecimento facial
# Copie este arquivo para config_custom.py e ajuste conforme necessário

from config import SystemConfig, ModelConfig, CameraConfig, ProcessingConfig, UIConfig

# Configuração personalizada
CUSTOM_CONFIG = SystemConfig(
    # Diretórios
    known_faces_dir="known_faces",
    embeddings_file="face_embeddings.pkl",
    log_file="logs/face_recognition.log",
    backup_dir="backups",
    
    # Modelo (escolha um)
    model=ModelConfig(
        name="Facenet",  # Opções: Facenet, VGG-Face, OpenFace, DeepFace
        detector_backend="mtcnn",  # Opções: opencv, mtcnn, retinaface
        threshold=0.6,
        enforce_detection=False
    ),
    
    # Câmera
    camera=CameraConfig(
        width=640,
        height=480,
        fps=30,
        frame_skip=2  # Processar a cada 2 frames
    ),
    
    # Processamento
    processing=ProcessingConfig(
        max_queue_size=3,
        batch_size=1,
        enable_gpu=True,
        memory_limit_mb=1024
    ),
    
    # Interface
    ui=UIConfig(
        window_title="Meu Sistema de Reconhecimento",
        show_fps=True,
        show_confidence=True,
        font_scale=0.6,
        font_thickness=2
    )
)

# Configurações específicas por ambiente

# Configuração para desenvolvimento (mais rápido)
DEV_CONFIG = SystemConfig(
    known_faces_dir="known_faces",
    embeddings_file="face_embeddings.pkl",
    log_file="logs/face_recognition_dev.log",
    backup_dir="backups",
    
    model=ModelConfig(
        name="Facenet",
        detector_backend="opencv",  # Mais rápido
        threshold=0.5,
        enforce_detection=False
    ),
    
    camera=CameraConfig(
        width=320,
        height=240,
        fps=15,
        frame_skip=3  # Processar menos frames
    ),
    
    processing=ProcessingConfig(
        max_queue_size=2,
        batch_size=1,
        enable_gpu=False,
        memory_limit_mb=512
    ),
    
    ui=UIConfig(
        window_title="Desenvolvimento - Reconhecimento Facial",
        show_fps=True,
        show_confidence=True,
        font_scale=0.5,
        font_thickness=1
    )
)

# Configuração para produção (mais preciso)
PROD_CONFIG = SystemConfig(
    known_faces_dir="known_faces",
    embeddings_file="face_embeddings.pkl",
    log_file="logs/face_recognition_prod.log",
    backup_dir="backups",
    
    model=ModelConfig(
        name="VGG-Face",  # Mais preciso
        detector_backend="retinaface",  # Mais preciso
        threshold=0.7,  # Mais restritivo
        enforce_detection=True
    ),
    
    camera=CameraConfig(
        width=1280,
        height=720,
        fps=30,
        frame_skip=1  # Processar todos os frames
    ),
    
    processing=ProcessingConfig(
        max_queue_size=5,
        batch_size=2,
        enable_gpu=True,
        memory_limit_mb=2048
    ),
    
    ui=UIConfig(
        window_title="Sistema de Reconhecimento Facial - Produção",
        show_fps=True,
        show_confidence=True,
        font_scale=0.7,
        font_thickness=2
    )
)

# Configuração para baixo consumo de recursos
LOW_RESOURCE_CONFIG = SystemConfig(
    known_faces_dir="known_faces",
    embeddings_file="face_embeddings.pkl",
    log_file="logs/face_recognition_low.log",
    backup_dir="backups",
    
    model=ModelConfig(
        name="OpenFace",  # Mais leve
        detector_backend="opencv",  # Mais leve
        threshold=0.5,
        enforce_detection=False
    ),
    
    camera=CameraConfig(
        width=320,
        height=240,
        fps=10,
        frame_skip=5  # Processar poucos frames
    ),
    
    processing=ProcessingConfig(
        max_queue_size=1,
        batch_size=1,
        enable_gpu=False,
        memory_limit_mb=256
    ),
    
    ui=UIConfig(
        window_title="Reconhecimento Facial - Baixo Consumo",
        show_fps=False,
        show_confidence=False,
        font_scale=0.4,
        font_thickness=1
    )
)

# Função para obter configuração baseada no ambiente
def get_config(environment="default"):
    """
    Obter configuração baseada no ambiente
    
    Args:
        environment: "default", "dev", "prod", "low_resource"
    
    Returns:
        SystemConfig: Configuração apropriada
    """
    configs = {
        "default": CUSTOM_CONFIG,
        "dev": DEV_CONFIG,
        "prod": PROD_CONFIG,
        "low_resource": LOW_RESOURCE_CONFIG
    }
    
    return configs.get(environment, CUSTOM_CONFIG)

# Exemplo de uso:
# from config_example import get_config
# config = get_config("prod")
# system = FaceRecognitionSystemV2(config)
