"""
Módulo de detecção de faces otimizado
"""
import cv2
import numpy as np
from typing import List, Tuple, Optional, Dict, Any
import time
from logger import logger

class FaceDetector:
    """Detector de faces otimizado com múltiplos backends"""
    
    def __init__(self, detector_backend: str = "mtcnn", confidence_threshold: float = 0.6):
        self.detector_backend = detector_backend
        self.confidence_threshold = confidence_threshold
        self.face_cascade = None
        self._init_detector()
    
    def initialize(self):
        """Inicializar detector de faces"""
        logger.info(f"Detector inicializado: {self.detector_backend}")
        return True
    
    def _init_detector(self):
        """Inicializar detector baseado no backend escolhido"""
        if self.detector_backend == "opencv":
            self.face_cascade = cv2.CascadeClassifier(
                cv2.data.haarcascades + 'haarcascade_frontalface_default.xml'
            )
        elif self.detector_backend in ("onnx", "retinaface", "yolo", "yolov5"):
            # Suporte básico: tentar onnxruntime se presente; caso contrário, fallback para OpenCV
            try:
                import onnxruntime as ort  # type: ignore
                # Nesta versão, mantemos fallback até que o modelo esteja configurado
                # (ex.: models/retinaface.onnx). Se não encontrar, cairá para OpenCV.
                self._onnx_available = True
            except Exception:
                self._onnx_available = False
                self.detector_backend = "opencv"
                self._init_detector()
                return
        logger.info(f"Detector inicializado: {self.detector_backend}")
    
    def detect_faces_opencv(self, frame: np.ndarray) -> List[Dict[str, Any]]:
        """Detecção usando OpenCV (mais rápida)"""
        if self.face_cascade is None:
            try:
                self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
            except Exception:
                return []
        gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
        faces = self.face_cascade.detectMultiScale(
            gray, 
            scaleFactor=1.1, 
            minNeighbors=5, 
            minSize=(30, 30),
            flags=cv2.CASCADE_SCALE_IMAGE
        )
        
        results = []
        for (x, y, w, h) in faces:
            # Extrair região da face
            face_region = frame[y:y+h, x:x+w]
            
            results.append({
                'bbox': (x, y, w, h),
                'confidence': 1.0,  # OpenCV não retorna confiança
                'landmarks': None,
                'face_array': face_region
            })
        
        return results
    
    def detect_faces_deepface(self, frame: np.ndarray) -> List[Dict[str, Any]]:
        """Detecção usando DeepFace (mais precisa)"""
        try:
            # Importar DeepFace sob demanda para evitar dependência quando não usado
            from deepface import DeepFace  # type: ignore
            face_objs = DeepFace.extract_faces(
                img_path=frame,
                detector_backend=self.detector_backend,
                enforce_detection=False,
                align=True
            )

            results = []
            for face_obj in face_objs:
                # Extrair coordenadas: DeepFace geralmente retorna 'facial_area'
                # Ex.: {'x': 10, 'y': 20, 'w': 100, 'h': 120}
                fa = face_obj.get('facial_area') or face_obj.get('region') or None
                if isinstance(fa, dict):
                    x = int(fa.get('x', 0))
                    y = int(fa.get('y', 0))
                    w = int(fa.get('w', 0)) or int(fa.get('width', 0) or 0)
                    h = int(fa.get('h', 0)) or int(fa.get('height', 0) or 0)
                    bbox = (x, y, w, h)
                else:
                    # fallback genérico
                    bbox = (0, 0, frame.shape[1] // 4, frame.shape[0] // 4)
                confidence = face_obj.get('confidence', 1.0)

                # Garantir que 'face' esteja no formato uint8 0-255
                results.append({
                    'bbox': bbox,
                    'confidence': confidence,
                    'landmarks': face_obj.get('landmarks'),
                    'face_array': face_obj['face']
                })

            return results

        except Exception as e:
            logger.error(f"Erro na detecção DeepFace: {e}")
            return []
    
    def detect_faces(self, frame: np.ndarray) -> List[Dict[str, Any]]:
        """Detectar faces usando o backend configurado"""
        start_time = time.time()
        
        if self.detector_backend == "opencv":
            results = self.detect_faces_opencv(frame)
        elif self.detector_backend in ("onnx", "retinaface", "yolo", "yolov5"):
            results = self.detect_faces_onnx(frame)
        else:
            results = self.detect_faces_deepface(frame)
        
        # Filtrar por confiança
        filtered_results = [
            r for r in results 
            if r['confidence'] >= self.confidence_threshold
        ]
        
        processing_time = time.time() - start_time
        logger.debug(f"Detectadas {len(filtered_results)} faces em {processing_time:.3f}s")
        
        return filtered_results

    def detect_faces_onnx(self, frame: np.ndarray) -> List[Dict[str, Any]]:
        """Detecção via ONNXRuntime quando disponível. Fallback: OpenCV."""
        try:
            # Verificar disponibilidade
            try:
                import onnxruntime as ort  # type: ignore
            except Exception:
                return self.detect_faces_opencv(frame)
            # Se houver modelo configurado no futuro, carregar e inferir aqui.
            # Enquanto não há modelo, usar fallback para manter o sistema operacional.
            return self.detect_faces_opencv(frame)
        except Exception as e:
            logger.warning(f"ONNX detect fallback (erro: {e})")
            return self.detect_faces_opencv(frame)
    
    def extract_face_region(self, frame: np.ndarray, bbox: Tuple[int, int, int, int]) -> np.ndarray:
        """Extrair região da face do frame"""
        x, y, w, h = bbox
        face_region = frame[y:y+h, x:x+w]
        return face_region
    
    def resize_face(self, face: np.ndarray, target_size: Tuple[int, int] = (160, 160)) -> np.ndarray:
        """Redimensionar face para tamanho padrão"""
        return cv2.resize(face, target_size)
    
    def preprocess_face(self, face: np.ndarray) -> np.ndarray:
        """Pré-processar face para melhor reconhecimento"""
        # Converter para RGB se necessário
        if len(face.shape) == 3 and face.shape[2] == 3:
            face = cv2.cvtColor(face, cv2.COLOR_BGR2RGB)
        
        # Normalizar
        face = face.astype(np.float32) / 255.0
        
        return face

class FaceEmbeddingExtractor:
    """Extrator de embeddings otimizado"""
    
    def __init__(self, model_name: str = "Facenet"):
        self.model_name = model_name
        self._warmup_model()
    
    def _warmup_model(self):
        """Aquecer o modelo para primeira inferência mais rápida"""
        try:
            # Usar uma imagem dummy para aquecer o modelo
            dummy_img = np.random.randint(0, 255, (160, 160, 3), dtype=np.uint8)
            self.extract_embedding(dummy_img)
            logger.info(f"Modelo {self.model_name} aquecido com sucesso")
        except Exception as e:
            logger.warning(f"Erro ao aquecer modelo: {e}")
    
    def extract_embedding(self, face) -> Optional[np.ndarray]:
        """Extrair embedding de uma face"""
        try:
            start_time = time.time()
            
            # Se recebeu um caminho de arquivo (string)
            if isinstance(face, str):
                face_path = face
            # Se a face já está no formato correto do DeepFace
            elif isinstance(face, dict) and 'face' in face:
                face_array = face['face']
                # Garantir que está no formato correto
                if face_array.dtype != np.uint8:
                    face_array = (face_array * 255).astype(np.uint8)
                face_path = face_array
            else:
                # Garantir que está no formato correto e convertido para RGB (frames OpenCV são BGR)
                if face.dtype != np.uint8:
                    face = (face * 255).astype(np.uint8)
                try:
                    face_rgb = cv2.cvtColor(face, cv2.COLOR_BGR2RGB)
                except Exception:
                    face_rgb = face
                face_path = face_rgb
            
            # Importação sob demanda
            from deepface import DeepFace  # type: ignore
            # Extrair embedding; usar detector 'skip' para faces já recortadas
            embedding_obj = DeepFace.represent(
                img_path=face_path,
                model_name=self.model_name,
                detector_backend='skip',
                enforce_detection=False
            )
            
            if embedding_obj and len(embedding_obj) > 0:
                embedding = np.array(embedding_obj[0]["embedding"])
                processing_time = time.time() - start_time
                logger.debug(f"Embedding extraído em {processing_time:.3f}s")
                return embedding
            else:
                logger.warning("Nenhuma face detectada para extração de embedding")
                return None
                
        except Exception as e:
            logger.error(f"Erro ao extrair embedding: {e}")
            return None
    
    def extract_embeddings_batch(self, faces: List[np.ndarray]) -> List[Optional[np.ndarray]]:
        """Extrair embeddings em lote (mais eficiente)"""
        embeddings = []
        for face in faces:
            embedding = self.extract_embedding(face)
            embeddings.append(embedding)
        return embeddings
