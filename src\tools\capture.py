import argparse
import time
from pathlib import Path

import cv2
import numpy as np

from src.face.engine import FaceEngine
from src.utils.paths import ensure_dir
from src.utils.face import cosine_similarity


def parse_args():
    p = argparse.ArgumentParser(description="Captura faces alinhadas para dataset")
    p.add_argument("--source", type=str, required=True, help="0 para webcam, ou URL RTSP")
    p.add_argument("--name", type=str, required=True, help="nome da identidade (pasta) a salvar")
    p.add_argument("--out", type=str, default="data/raw_faces", help="pasta base de saída")
    p.add_argument("--save-every", type=int, default=10, help="salva a cada N frames detectados")
    p.add_argument("--min-score", type=float, default=0.6, help="score mínimo de detecção")
    p.add_argument("--resize", type=int, default=None, help="redimensiona maior lado do frame")
    return p.parse_args()


def open_source(source: str):
    try:
        cam_index = int(source)
        cap = cv2.VideoCapture(cam_index, cv2.CAP_DSHOW)
    except ValueError:
        cap = cv2.VideoCapture(source)
    return cap


def main():
    args = parse_args()
    cap = open_source(args.source)
    if not cap.isOpened():
        raise RuntimeError(f"Não foi possível abrir a fonte: {args.source}")

    out_dir = ensure_dir(Path(args.out) / args.name)
    engine = FaceEngine(model_pack="buffalo_l", det_size=(640, 640))

    win_name = f"Capture — {args.name}"
    cv2.namedWindow(win_name, cv2.WINDOW_NORMAL)
    cv2.resizeWindow(win_name, 960, 540)

    saved = 0
    last_emb = None
    frame_count = 0

    while True:
        ok, frame = cap.read()
        if not ok:
            break

        frame_count += 1
        if args.resize is not None:
            h, w = frame.shape[:2]
            max_side = max(h, w)
            if max_side > args.resize:
                scale = args.resize / float(max_side)
                frame = cv2.resize(frame, (int(w * scale), int(h * scale)))

        faces = engine.detect_and_embed(frame)
        show = frame.copy()

        if len(faces) > 0:
            # escolhe a maior face (área)
            areas = [(f["bbox"][2] - f["bbox"][0]) * (f["bbox"][3] - f["bbox"][1]) for f in faces]
            idx = int(np.argmax(areas))
            f = faces[idx]

            if f["det_score"] >= args.min_score:
                x1, y1, x2, y2 = f["bbox"]
                cv2.rectangle(show, (x1, y1), (x2, y2), (0, 255, 0), 2)
                cv2.putText(show, f"score {f['det_score']:.2f}", (x1, y1 - 4), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0,0,0), 1, cv2.LINE_AA)

                if frame_count % args.save_every == 0:
                    emb = f["embedding"]
                    if last_emb is None or cosine_similarity(emb, last_emb) < 0.98:
                        chip = f["chip"]  # 112x112 alinhada
                        ts = int(time.time() * 1000)
                        out_path = out_dir / f"{args.name}_{ts}.jpg"
                        cv2.imwrite(str(out_path), cv2.cvtColor(chip, cv2.COLOR_RGB2BGR))
                        last_emb = emb.copy()
                        saved += 1

        cv2.putText(show, f"salvos: {saved}", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0,255,0), 2, cv2.LINE_AA)
        cv2.imshow(win_name, show)
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break

    cap.release()
    cv2.destroyAllWindows()


if __name__ == "__main__":
    main()

