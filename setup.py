"""
Script de instalação e configuração do sistema de reconhecimento facial
"""
import os
import sys
import subprocess
import platform
from pathlib import Path

def check_python_version():
    """Verificar versão do Python"""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ é necessário")
        print(f"   Versão atual: {sys.version}")
        return False
    print(f"✅ Python {sys.version.split()[0]} detectado")
    return True

def check_system_requirements():
    """Verificar requisitos do sistema"""
    print("\n🔍 Verificando requisitos do sistema...")
    
    # Verificar sistema operacional
    os_name = platform.system()
    print(f"   Sistema Operacional: {os_name}")
    
    # Verificar memória RAM
    try:
        import psutil
        memory = psutil.virtual_memory()
        memory_gb = memory.total / (1024**3)
        print(f"   Memória RAM: {memory_gb:.1f} GB")
        
        if memory_gb < 4:
            print("⚠️  Recomendado: 4GB+ de RAM")
        else:
            print("✅ Memória RAM suficiente")
    except ImportError:
        print("⚠️  psutil não instalado, não foi possível verificar memória")
    
    # Verificar GPU (opcional)
    try:
        import tensorflow as tf
        gpus = tf.config.list_physical_devices('GPU')
        if gpus:
            print(f"✅ GPU detectada: {len(gpus)} dispositivo(s)")
        else:
            print("ℹ️  Nenhuma GPU detectada (CPU será usado)")
    except ImportError:
        print("ℹ️  TensorFlow não instalado ainda")
    
    return True

def install_dependencies():
    """Instalar dependências"""
    print("\n📦 Instalando dependências...")
    
    try:
        # Atualizar pip
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        print("✅ pip atualizado")
        
        # Instalar dependências
        subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                      check=True, capture_output=True)
        print("✅ Dependências instaladas")
        
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Erro ao instalar dependências: {e}")
        return False

def create_directories():
    """Criar diretórios necessários"""
    print("\n📁 Criando diretórios...")
    
    directories = [
        "known_faces",
        "backups",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Diretório {directory}/ criado")
    
    return True

def test_installation():
    """Testar instalação"""
    print("\n🧪 Testando instalação...")
    
    try:
        # Testar imports principais
        import cv2
        print("✅ OpenCV importado")
        
        import numpy as np
        print("✅ NumPy importado")
        
        import deepface
        print("✅ DeepFace importado")
        
        import tensorflow as tf
        print("✅ TensorFlow importado")
        
        # Testar tkinter (pode não estar disponível em alguns ambientes)
        try:
            import tkinter as tk
            print("✅ Tkinter disponível")
        except ImportError:
            print("⚠️  Tkinter não disponível (interface gráfica limitada)")
        
        # Testar câmera
        cap = cv2.VideoCapture(0)
        if cap.isOpened():
            print("✅ Câmera detectada")
            cap.release()
        else:
            print("⚠️  Câmera não detectada")
        
        return True
    except ImportError as e:
        print(f"❌ Erro ao importar: {e}")
        return False
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        return False

def create_sample_config():
    """Criar arquivo de configuração de exemplo"""
    print("\n⚙️  Criando configuração de exemplo...")
    
    config_content = '''# Configuração de exemplo para o sistema de reconhecimento facial
# Copie este arquivo para config_custom.py e ajuste conforme necessário

from config import SystemConfig, ModelConfig, CameraConfig, ProcessingConfig, UIConfig

# Configuração personalizada
CUSTOM_CONFIG = SystemConfig(
    # Diretórios
    known_faces_dir="known_faces",
    embeddings_file="face_embeddings.pkl",
    log_file="logs/face_recognition.log",
    backup_dir="backups",
    
    # Modelo (escolha um)
    model=ModelConfig(
        name="Facenet",  # Opções: Facenet, VGG-Face, OpenFace, DeepFace
        detector_backend="mtcnn",  # Opções: opencv, mtcnn, retinaface
        threshold=0.6,
        enforce_detection=False
    ),
    
    # Câmera
    camera=CameraConfig(
        width=640,
        height=480,
        fps=30,
        frame_skip=2  # Processar a cada 2 frames
    ),
    
    # Processamento
    processing=ProcessingConfig(
        max_queue_size=3,
        batch_size=1,
        enable_gpu=True,
        memory_limit_mb=1024
    ),
    
    # Interface
    ui=UIConfig(
        window_title="Meu Sistema de Reconhecimento",
        show_fps=True,
        show_confidence=True,
        font_scale=0.6,
        font_thickness=2
    )
)
'''
    
    with open("config_example.py", "w", encoding="utf-8") as f:
        f.write(config_content)
    
    print("✅ Arquivo config_example.py criado")
    return True

def show_usage_instructions():
    """Mostrar instruções de uso"""
    print("\n" + "="*60)
    print("🎉 INSTALAÇÃO CONCLUÍDA COM SUCESSO!")
    print("="*60)
    
    print("\n📋 PRÓXIMOS PASSOS:")
    print("1. Adicione fotos das pessoas conhecidas na pasta 'known_faces/'")
    print("   - Nomeie os arquivos com o nome da pessoa (ex: joao.jpg)")
    print("   - Use fotos de boa qualidade e iluminação")
    
    print("\n2. Execute o sistema:")
    print("   python face_recognition_system_v2.py")
    
    print("\n3. Controles durante a execução:")
    print("   - 'q': Sair")
    print("   - 'r': Recarregar embeddings")
    print("   - 's': Salvar frame")
    print("   - 'c': Mostrar estatísticas")
    print("   - 'h': Limpar cache")
    
    print("\n4. Para personalizar configurações:")
    print("   - Edite config_example.py")
    print("   - Renomeie para config_custom.py")
    print("   - Modifique face_recognition_system_v2.py para usar sua configuração")
    
    print("\n📚 DOCUMENTAÇÃO:")
    print("   - README.md: Documentação completa")
    print("   - config.py: Opções de configuração")
    print("   - logs/: Logs do sistema")
    
    print("\n🔧 SOLUÇÃO DE PROBLEMAS:")
    print("   - Verifique os logs em face_recognition.log")
    print("   - Execute: python test_system.py")
    print("   - Consulte o README.md para problemas comuns")
    
    print("\n" + "="*60)

def main():
    """Função principal de instalação"""
    print("🚀 INSTALADOR DO SISTEMA DE RECONHECIMENTO FACIAL")
    print("="*60)
    
    # Verificar Python
    if not check_python_version():
        sys.exit(1)
    
    # Verificar requisitos do sistema
    check_system_requirements()
    
    # Instalar dependências
    if not install_dependencies():
        print("\n❌ Falha na instalação das dependências")
        print("   Tente instalar manualmente: pip install -r requirements.txt")
        sys.exit(1)
    
    # Criar diretórios
    create_directories()
    
    # Testar instalação
    if not test_installation():
        print("\n❌ Falha no teste de instalação")
        print("   Verifique as dependências instaladas")
        sys.exit(1)
    
    # Criar configuração de exemplo
    create_sample_config()
    
    # Mostrar instruções
    show_usage_instructions()

if __name__ == "__main__":
    main()
