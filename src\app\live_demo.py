import argparse
from typing import Optional, <PERSON>ple

import cv2
import numpy as np

from src.face.engine import FaceE<PERSON><PERSON>
from src.utils.face import cosine_sim_matrix


def parse_args():
    p = argparse.ArgumentParser(description="Live face detection + recognition demo")
    p.add_argument("--source", type=str, required=True, help="0 para webcam, ou URL RTSP")
    p.add_argument("--gallery", type=str, default=None, help="artifacts/gallery.npz (opcional)")
    p.add_argument("--threshold", type=float, default=0.35, help="limiar de similaridade (cos)")
    p.add_argument("--resize", type=int, default=None, help="redimensiona maior lado do frame")
    p.add_argument("--min-score", type=float, default=0.6, help="score mínimo de detecção")
    return p.parse_args()


def open_source(source: str):
    # Se for número, abre webcam
    try:
        cam_index = int(source)
        cap = cv2.VideoCapture(cam_index, cv2.CAP_DSHOW)
    except ValueError:
        # RTSP/arquivo
        cap = cv2.VideoCapture(source)
    return cap


def load_gallery(path: Optional[str]):
    if not path:
        return None, None
    data = np.load(path, allow_pickle=True)
    names = data["names"]
    embs = data["embeddings"].astype(np.float32)
    # embeddings já normalizados
    return names, embs


def draw_bbox(frame, bbox, color=(0, 255, 0), thickness=2):
    x1, y1, x2, y2 = bbox
    cv2.rectangle(frame, (x1, y1), (x2, y2), color, thickness)


def put_label(frame, text, org, bg=(0, 0, 0)):
    font = cv2.FONT_HERSHEY_SIMPLEX
    scale = 0.5
    thickness = 1
    (w, h), baseline = cv2.getTextSize(text, font, scale, thickness)
    x, y = org
    cv2.rectangle(frame, (x, y - h - baseline), (x + w, y + baseline), bg, -1)
    cv2.putText(frame, text, (x, y), font, scale, (255, 255, 255), thickness, cv2.LINE_AA)


def main():
    args = parse_args()
    cap = open_source(args.source)
    if not cap.isOpened():
        raise RuntimeError(f"Não foi possível abrir a fonte: {args.source}")

    engine = FaceEngine(model_pack="buffalo_l", det_size=(640, 640))
    names, gallery = load_gallery(args.gallery)

    win_name = f"Live — {args.source}"
    cv2.namedWindow(win_name, cv2.WINDOW_NORMAL)
    cv2.resizeWindow(win_name, 960, 540)

    while True:
        ok, frame = cap.read()
        if not ok:
            break

        # opcional resize para reduzir latência
        if args.resize is not None:
            h, w = frame.shape[:2]
            max_side = max(h, w)
            if max_side > args.resize:
                scale = args.resize / float(max_side)
                frame = cv2.resize(frame, (int(w * scale), int(h * scale)))

        faces = engine.detect_and_embed(frame)

        show = frame.copy()
        labels = []
        if gallery is not None and len(faces) > 0:
            query = np.stack([f["embedding"] for f in faces], axis=0)
            sims = cosine_sim_matrix(query, gallery)
            best_idx = np.argmax(sims, axis=1)
            best_sim = sims[np.arange(len(faces)), best_idx]
            for i, f in enumerate(faces):
                name = names[best_idx[i]] if best_sim[i] >= args.threshold else "desconhecido"
                labels.append((name, best_sim[i]))
        else:
            labels = [("face", f["det_score"]) for f in faces]

        for i, f in enumerate(faces):
            if f["det_score"] < args.min_score:
                continue
            bbox = f["bbox"]
            draw_bbox(show, bbox, (0, 255, 0))
            x1, y1, x2, y2 = bbox
            tag = labels[i][0]
            sim = labels[i][1]
            put_label(show, f"{tag} {sim:.2f}", (x1, y1 - 4), (0, 0, 0))

        cv2.imshow(win_name, show)
        key = cv2.waitKey(1) & 0xFF
        if key == ord('q'):
            break

    cap.release()
    cv2.destroyAllWindows()


if __name__ == "__main__":
    main()

