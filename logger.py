"""
Sistema de logging para o reconhecimento facial
"""
import logging
import os
from logging.handlers import RotatingFileHandler
from typing import Optional


class FaceRecognitionLogger:
    """Logger personalizado para o sistema de reconhecimento facial.

    Melhorias:
    - Rotação de logs (5 MB, 3 backups)
    - Criação automática do diretório do arquivo de log
    - Configuração via variáveis de ambiente: LOG_FILE e LOG_LEVEL
    - Evita duplicação de handlers e reusa console handler
    """

    def __init__(self, log_file: Optional[str] = None, level: Optional[int] = None):
        env_log_file = os.environ.get("LOG_FILE")
        env_log_level = os.environ.get("LOG_LEVEL", "INFO").upper()

        # Definir caminho e nível
        self.log_file = os.path.normpath(log_file or env_log_file or "face_recognition.log")
        self.level = level if isinstance(level, int) else getattr(logging, env_log_level, logging.INFO)

        self._setup_logger()

    def _setup_logger(self):
        """Configurar o logger com rotação e console handler."""
        logger_name = "FaceRecognition"  # nome estável para facilitar consulta
        self.logger = logging.getLogger(logger_name)
        self.logger.setLevel(self.level)
        self.logger.propagate = False

        # Formatter consistente
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )

        # Garantir diretório do arquivo de log
        log_dir = os.path.dirname(self.log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)

        # Verificar e (re)configurar file handler
        need_file_handler = True
        for h in list(self.logger.handlers):
            # Atualizar nível do handler existente
            try:
                h.setLevel(self.level)
            except Exception:
                pass

            if isinstance(h, logging.FileHandler):
                # Se já está apontando para o mesmo arquivo, reaproveitar
                try:
                    if os.path.normcase(getattr(h, "baseFilename", "")) == os.path.normcase(
                        os.path.abspath(self.log_file)
                    ):
                        need_file_handler = False
                    else:
                        # Handler de arquivo para outro destino: remover
                        self.logger.removeHandler(h)
                        h.close()
                except Exception:
                    # Em caso de erro, forçar criação de um novo handler
                    self.logger.removeHandler(h)
                    try:
                        h.close()
                    except Exception:
                        pass

        if need_file_handler:
            file_handler = RotatingFileHandler(
                self.log_file, maxBytes=5 * 1024 * 1024, backupCount=3, encoding="utf-8"
            )
            file_handler.setLevel(self.level)
            file_handler.setFormatter(formatter)
            self.logger.addHandler(file_handler)

        # Garantir console handler único
        has_console = any(isinstance(h, logging.StreamHandler) and not isinstance(h, logging.FileHandler) for h in self.logger.handlers)
        if not has_console:
            console_handler = logging.StreamHandler()
            console_handler.setLevel(max(logging.INFO, self.level))
            console_handler.setFormatter(formatter)
            self.logger.addHandler(console_handler)

    # Métodos utilitários
    def info(self, message: str):
        self.logger.info(message)

    def warning(self, message: str):
        self.logger.warning(message)

    def error(self, message: str, exc_info: bool = False):
        self.logger.error(message, exc_info=exc_info)

    def debug(self, message: str):
        self.logger.debug(message)

    def log_recognition(self, person_name: str, confidence: float, processing_time: float):
        self.info(
            f"Reconhecimento: {person_name} (confiança: {confidence:.3f}, tempo: {processing_time:.3f}s)"
        )

    def log_performance(self, fps: float, queue_size: int, memory_usage: float):
        self.debug(
            f"Performance - FPS: {fps:.1f}, Queue: {queue_size}, Memória: {memory_usage:.1f}MB"
        )

    def log_error_with_context(self, operation: str, error: Exception, context: dict = None):
        context_str = f" | Contexto: {context}" if context else ""
        self.error(f"Erro em {operation}: {str(error)}{context_str}", exc_info=True)


# Instância global do logger
logger = FaceRecognitionLogger()
