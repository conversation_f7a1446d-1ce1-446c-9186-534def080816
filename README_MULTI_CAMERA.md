# 🎥 Sistema de Reconhecimento Facial com Múltiplas Câmeras

Sistema avançado de reconhecimento facial que suporta múltiplas câmeras simultâneas, incluindo câmeras locais e streams RTSP, com divisão horizontal do espaço de exibição.

## ✨ Funcionalidades

### 🎯 **Múltiplas Câmeras**
- **Câmeras Locais**: Suporte para câmeras USB (índices 0, 1, 2...)
- **Streams RTSP**: Suporte para câmeras IP via protocolo RTSP
- **Arquivos de Vídeo**: Suporte para arquivos de vídeo locais
- **Divisão Horizontal**: Espaço dividido automaticamente entre câmeras

### 🔧 **Recursos Avançados**
- **Processamento Assíncrono**: Cada câmera processada em thread separada
- **Reconhecimento Facial**: Detecção e reconhecimento em tempo real
- **Análise de Emoções**: Detecção de 7 emoções diferentes
- **Detecção Demográfica**: Idade e gênero
- **Controle de Presença**: Registro automático de presenças
- **Gravação de Vídeo**: Eventos importantes gravados automaticamente
- **Analytics**: Estatísticas detalhadas por câmera

### 📊 **Interface de Exibição**
- **Layout Responsivo**: Divisão horizontal automática
- **Informações por Câmera**: ID, status, FPS individual
- **Estatísticas Globais**: FPS total, reconhecimentos, uso de recursos
- **Indicadores Visuais**: Cores baseadas em confiança
- **Barra de Progresso**: Para demonstrações

## 🚀 Instalação

### Pré-requisitos
```bash
# Instalar dependências
pip install -r requirements.txt

# Verificar instalação
python setup.py
```

### Configuração de Câmeras

1. **Editar `multi_camera_config.py`**:
```python
# Adicionar suas câmeras RTSP
'rtsp_1': CameraConfig(
    id='rtsp_1',
    type='rtsp',
    path='rtsp://admin:password@*************:554/stream1',
    width=640,
    height=480,
    description='Câmera IP 1'
),
```

2. **Configurar URLs RTSP**:
   - Substitua `admin:password` pelas credenciais da sua câmera
   - Substitua `*************` pelo IP da sua câmera
   - Ajuste a porta se necessário (padrão: 554)

## 📖 Uso

### 1. **Listar Câmeras Disponíveis**
```bash
python run_without_gui.py multi-camera --list-cameras
```

### 2. **Executar com Câmera Local Padrão**
```bash
python run_without_gui.py multi-camera --duration 60
```

### 3. **Executar com Câmeras Específicas**
```bash
# Apenas câmera local
python run_without_gui.py multi-camera --cameras local_0

# Câmera local + RTSP
python run_without_gui.py multi-camera --cameras local_0 rtsp_1

# Múltiplas câmeras RTSP
python run_without_gui.py multi-camera --cameras rtsp_1 rtsp_2
```

### 4. **Executar Sistema Direto**
```bash
# Sistema básico
python multi_camera_system.py

# Com câmeras específicas
python multi_camera_system.py --cameras cam1:local:0 cam2:rtsp:rtsp://url
```

### 5. **Demonstração Completa**
```bash
python demo_multi_camera.py --duration 120 --cameras local_0 rtsp_1
```

## ⚙️ Configuração

### **Arquivo `multi_camera_config.py`**

```python
# Exemplo de configuração de câmera RTSP
'rtsp_1': CameraConfig(
    id='rtsp_1',                    # ID único
    type='rtsp',                    # Tipo: 'local' ou 'rtsp'
    path='rtsp://user:pass@ip:port/stream',  # URL RTSP
    width=640,                      # Largura
    height=480,                     # Altura
    enabled=True,                   # Habilitada
    description='Câmera IP 1'       # Descrição
)
```

### **Formatos de URL RTSP Suportados**
```
# Formato básico
rtsp://ip:port/stream

# Com autenticação
rtsp://username:password@ip:port/stream

# Com porta específica
rtsp://username:password@*************:554/stream1

# Exemplos reais
rtsp://admin:123456@*************:554/stream1
rtsp://user:pass@*********:8554/live
```

## 🎮 Controles

### **Teclas de Atalho**
- **`q`**: Sair do sistema
- **`ESC`**: Sair do sistema
- **`Space`**: Pausar/retomar processamento

### **Interface Visual**
- **Verde**: Reconhecimento com alta confiança (>70%)
- **Amarelo**: Reconhecimento com confiança média (50-70%)
- **Vermelho**: Reconhecimento com baixa confiança (<50%)
- **Branco**: Face desconhecida

## 📊 Monitoramento

### **Estatísticas em Tempo Real**
- **FPS**: Quadros por segundo
- **Reconhecimentos**: Total de faces reconhecidas
- **Câmeras Ativas**: Número de câmeras funcionando
- **CPU/RAM**: Uso de recursos do sistema

### **Logs Detalhados**
```bash
# Ver logs em tempo real
tail -f logs/face_recognition.log

# Filtrar por câmera específica
grep "camera_id" logs/face_recognition.log
```

## 🔧 Solução de Problemas

### **Problemas Comuns**

1. **Câmera RTSP não conecta**
   ```bash
   # Verificar conectividade
   ping *************
   
   # Testar URL RTSP
   ffplay rtsp://admin:password@*************:554/stream1
   ```

2. **Câmera local não funciona**
   ```bash
   # Listar dispositivos de vídeo
   ls /dev/video*
   
   # Testar câmera específica
   python -c "import cv2; cap = cv2.VideoCapture(0); print(cap.isOpened())"
   ```

3. **Performance baixa**
   - Reduza resolução das câmeras
   - Aumente `skip_frames` na configuração
   - Use menos câmeras simultâneas

4. **Erro de memória**
   - Reduza `buffer_size` nas filas
   - Aumente `skip_frames`
   - Feche outras aplicações

### **Logs de Debug**
```python
# Ativar logs detalhados
import logging
logging.getLogger().setLevel(logging.DEBUG)
```

## 📈 Performance

### **Recomendações por Hardware**

| Hardware | Câmeras Recomendadas | Resolução | FPS |
|----------|---------------------|-----------|-----|
| **Baixo** | 1-2 | 320x240 | 15-20 |
| **Médio** | 2-4 | 640x480 | 20-30 |
| **Alto** | 4-8 | 640x480 | 30+ |
| **Profissional** | 8+ | 1280x720 | 30+ |

### **Otimizações**
```python
# Configuração otimizada para múltiplas câmeras
ProcessingConfig(
    max_fps=20,           # FPS máximo por câmera
    skip_frames=3,        # Processar 1 a cada 3 frames
    confidence_threshold=0.7,  # Limiar mais alto
    enable_emotion=False,      # Desabilitar se não necessário
    enable_age_gender=False,   # Desabilitar se não necessário
)
```

## 🔒 Segurança

### **URLs RTSP Seguras**
- Use HTTPS quando possível
- Configure autenticação forte
- Use VPN para acesso remoto
- Monitore logs de acesso

### **Dados Sensíveis**
- Não commite credenciais no código
- Use variáveis de ambiente
- Criptografe gravações de vídeo
- Implemente retenção de dados

## 📝 Exemplos de Uso

### **Monitoramento de Escritório**
```bash
# 2 câmeras: entrada + área comum
python run_without_gui.py multi-camera \
  --cameras local_0 rtsp_1 \
  --duration 480  # 8 horas
```

### **Demonstração de Evento**
```bash
# 4 câmeras: múltiplos ângulos
python run_without_gui.py multi-camera \
  --cameras local_0 local_1 rtsp_1 rtsp_2 \
  --duration 120  # 2 minutos
```

### **Monitoramento Residencial**
```bash
# Câmeras externas via RTSP
python run_without_gui.py multi-camera \
  --cameras rtsp_front rtsp_back rtsp_garage \
  --duration 0  # Contínuo
```

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para detalhes.

## 🆘 Suporte

- **Issues**: Abra uma issue no GitHub
- **Documentação**: Consulte os READMEs específicos
- **Logs**: Verifique os logs para diagnóstico
- **Testes**: Execute `python run_without_gui.py test`

---

**🎉 Aproveite o sistema de múltiplas câmeras!**
